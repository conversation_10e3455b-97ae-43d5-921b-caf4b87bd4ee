# Chat Wizard Scribe Hub ✨

A modern, feature-rich chat application with **enhanced visual responses**, **interactive code examples**, and **rich formatting capabilities**. Built with React, TypeScript, and Vite.

## 🎯 Enhanced Features

### 🎨 Rich Visual Responses
- **Enhanced Markdown Rendering** with beautiful typography and formatting
- **Syntax-Highlighted Code Blocks** with copy functionality and language detection
- **Live Code Previews** for HTML, CSS, and JavaScript with real-time rendering
- **Interactive Demonstrations** with adjustable parameters
- **Visual Diagrams** including flowcharts, charts, and SVG illustrations
- **Mathematical Expressions** with LaTeX support using KaTeX

### 🚀 Interactive Code Examples
- **Live HTML/CSS/JS Previews** - See code results in real-time
- **Editable Code Blocks** - Modify examples and see instant updates
- **Tabbed Interface** - Switch between code, preview, and different languages
- **Fullscreen Mode** - Expand previews for better viewing
- **Copy to Clipboard** - Easy code sharing and reuse

### 🎮 Interactive Demonstrations
- **CSS Animation Controls** - Adjust duration, delay, timing functions
- **Color Picker Interface** - Explore HSL color space interactively
- **Layout Playground** - Experiment with Flexbox properties
- **Parameter Sliders** - Real-time value adjustments
- **Reset Functionality** - Return to default settings

### 📊 Visual Diagrams & Charts
- **Flowchart Generation** - Process flows and decision trees
- **Data Visualization** - Interactive charts and graphs
- **SVG Illustrations** - Custom diagrams and technical drawings
- **Mermaid Support** - Diagram-as-code rendering (placeholder implementation)
- **Export Functionality** - Download diagrams as SVG files

## Project info

**URL**: https://lovable.dev/projects/220e371f-6ff1-4ea7-af7d-2c478dfd1bfb

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/220e371f-6ff1-4ea7-af7d-2c478dfd1bfb) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

## 🎯 How to Use Enhanced Features

### 💬 Requesting Enhanced Responses

To trigger the enhanced visual features, use these types of prompts:

**For Live Code Previews:**
- "Show me an interactive HTML example"
- "Create a CSS animation demo"
- "Build a responsive card component"
- "Generate a JavaScript color picker"

**For Interactive Demonstrations:**
- "Create an interactive CSS animation demo" (add `<!-- INTERACTIVE:CSS_ANIMATION -->`)
- "Show me a color picker interface" (add `<!-- INTERACTIVE:COLOR_PICKER -->`)
- "Explain flexbox with an interactive demo" (add `<!-- INTERACTIVE:LAYOUT_DEMO -->`)

**For Visual Diagrams:**
- "Create a flowchart showing the process"
- "Generate a diagram explaining the concept"
- "Show me a visual representation"

**For Mathematical Expressions:**
- Use LaTeX syntax: `$$E = mc^2$$` for block math
- Use inline math: `$\pi r^2$` for inline expressions

### 🛠️ Technical Implementation

The enhanced features are implemented using:

**Core Technologies:**
- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

**Enhanced Response Libraries:**
- `react-markdown` - Enhanced markdown rendering
- `react-syntax-highlighter` - Code syntax highlighting
- `remark-gfm` - GitHub Flavored Markdown support
- `remark-math` & `rehype-katex` - Mathematical expressions
- `rehype-raw` - Raw HTML support in markdown
- `prismjs` - Additional syntax highlighting themes

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/220e371f-6ff1-4ea7-af7d-2c478dfd1bfb) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
