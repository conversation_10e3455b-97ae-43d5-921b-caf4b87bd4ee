# Enhanced Chat Response Features - Sample Prompts

This file contains sample prompts that demonstrate the enhanced visual response capabilities of the Chat Wizard application.

## 🎨 Rich Markdown & Code Examples

### Basic Formatting Demo
```
Can you show me how to create a beautiful card component with modern CSS? Include both the HTML structure and CSS styling with explanations.
```

### Live Code Preview Demo
```
Create an interactive HTML page that demonstrates CSS Grid layout with a responsive design. Include hover effects and smooth transitions.
```

### Syntax Highlighting Demo
```
Show me a JavaScript function that creates a dynamic color palette generator. Include comments explaining each part of the code.
```

## 🎮 Interactive Demonstrations

### CSS Animation Demo
```
Create an interactive CSS animation demo where I can adjust the duration, delay, and timing function to see how they affect the animation.

<!-- INTERACTIVE:CSS_ANIMATION -->
```

### Color Picker Demo
```
Build an interactive color picker that lets me explore HSL color values by adjusting hue, saturation, and lightness with sliders.

<!-- INTERACTIVE:COLOR_PICKER -->
```

### Layout Demo
```
Explain CSS Flexbox with an interactive demonstration where I can change flex properties and see the results in real-time.

<!-- INTERACTIVE:LAYOUT_DEMO -->
```

## 📊 Visual Diagrams & Charts

### Flowchart Demo
```
Create a visual flowchart that explains the process of how a web browser renders a webpage, from DNS lookup to final display.

<!-- DIAGRAM -->
```

### Mermaid Diagram Demo
```
Generate a Mermaid diagram showing the architecture of a modern web application with frontend, backend, and database components.

```mermaid
graph TD
    A[User] --> B[Frontend React App]
    B --> C[API Gateway]
    C --> D[Backend Services]
    D --> E[Database]
    D --> F[External APIs]
```

### Mathematical Expressions Demo
```
Explain the quadratic formula with both the mathematical notation and a visual representation:

The quadratic formula is: $$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$

Where $a$, $b$, and $c$ are coefficients of the quadratic equation $ax^2 + bx + c = 0$.
```

## 🚀 Advanced Examples

### Complete Web Component Demo
```
Create a complete, interactive web component that demonstrates modern web development practices. Include:

1. HTML structure with semantic elements
2. CSS with custom properties and modern layout techniques
3. JavaScript with event handling and DOM manipulation
4. Responsive design principles
5. Accessibility considerations

Make it a functional todo list application with add, edit, delete, and filter capabilities.
```

### Data Visualization Demo
```
Build an interactive data visualization that shows website analytics data. Include:

- Bar charts for page views
- Line graphs for user engagement over time
- Interactive filters and controls
- Responsive design for mobile devices
- Smooth animations and transitions

Use sample data and make all elements interactive.
```

### CSS Framework Comparison
```
Create a visual comparison of different CSS layout methods (Flexbox vs Grid vs Float) with interactive examples showing:

- When to use each method
- Pros and cons of each approach
- Live code examples that users can modify
- Visual diagrams explaining the concepts
- Best practices and common pitfalls
```

## 💡 Tips for Best Results

1. **Be Specific**: The more detailed your request, the better the enhanced features will work
2. **Use Keywords**: Include words like "interactive", "visual", "demo", "example" to trigger enhanced rendering
3. **Request Code**: Ask for HTML, CSS, or JavaScript examples to see live previews
4. **Ask for Diagrams**: Request flowcharts, charts, or visual explanations for diagram generation
5. **Include Math**: Use LaTeX syntax for mathematical expressions and formulas

## 🎯 Feature Triggers

- **Live Previews**: Triggered by HTML, CSS, or JavaScript code blocks
- **Interactive Demos**: Triggered by special HTML comments like `<!-- INTERACTIVE:TYPE -->`
- **Diagrams**: Triggered by `<!-- DIAGRAM -->` or Mermaid code blocks
- **Math**: Triggered by LaTeX syntax `$$...$$` or `$...$`
- **Enhanced Formatting**: Automatically applied to all markdown content

Try these prompts in the chat to see the enhanced features in action!
