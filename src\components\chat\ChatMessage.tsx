
import React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Bo<PERSON>, User } from 'lucide-react';
import { MessageRenderer } from './MessageRenderer';

interface Message {
  id: string;
  content: string | Array<{type: string, text: string}>;
  role: 'user' | 'assistant';
  timestamp: Date;
  model?: string;
  images?: string[];
  isStreaming?: boolean;
}

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.role === 'user';

  // Helper function to extract text content from different formats
  const getMessageText = (content: string | Array<{type: string, text: string}>): string => {
    if (typeof content === 'string') {
      return content;
    }

    if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('');
    }

    return '';
  };

  const messageText = getMessageText(message.content);

  return (
    <div className={`flex gap-4 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <Avatar className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600">
          <AvatarFallback className="bg-transparent">
            <Bot className="w-4 h-4 text-white" />
          </AvatarFallback>
        </Avatar>
      )}

      <div className={`flex flex-col max-w-3xl ${isUser ? 'items-end' : 'items-start'}`}>
        <div className={`rounded-2xl p-4 ${
          isUser
            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
            : 'bg-white/10 backdrop-blur-lg text-white'
        }`}>
          {/* Display images if present */}
          {message.images && message.images.length > 0 && (
            <div className="mb-3 grid grid-cols-2 gap-2 max-w-md">
              {message.images.map((imageUrl, index) => (
                <img
                  key={index}
                  src={imageUrl}
                  alt={`Uploaded image ${index + 1}`}
                  className="rounded-lg max-h-48 object-cover"
                />
              ))}
            </div>
          )}

          {/* Enhanced message rendering */}
          {isUser ? (
            <div className="whitespace-pre-wrap">
              {messageText}
            </div>
          ) : (
            <MessageRenderer
              content={messageText}
              isStreaming={message.isStreaming}
            />
          )}
        </div>

        <div className="flex items-center gap-2 mt-2 text-xs text-white/60">
          <span>{message.timestamp.toLocaleTimeString()}</span>
          {message.model && (
            <>
              <span>•</span>
              <span>{message.model}</span>
            </>
          )}
          {message.isStreaming && (
            <>
              <span>•</span>
              <span className="text-blue-400">Streaming...</span>
            </>
          )}
        </div>
      </div>

      {isUser && (
        <Avatar className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600">
          <AvatarFallback className="bg-transparent">
            <User className="w-4 h-4 text-white" />
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};
