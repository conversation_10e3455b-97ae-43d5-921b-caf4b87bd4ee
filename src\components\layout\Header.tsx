
import React from 'react';
import { Button } from '@/components/ui/button';
import { Menu, Settings, User } from 'lucide-react';
import { ModelSelector } from '@/components/chat/ModelSelector';

interface HeaderProps {
  onToggleSidebar: () => void;
  selectedModel: string;
  onModelChange: (model: string) => void;
}

export const Header: React.FC<HeaderProps> = ({ 
  onToggleSidebar, 
  selectedModel, 
  onModelChange 
}) => {
  return (
    <header className="bg-white/10 backdrop-blur-lg border-b border-white/20 px-4 py-3 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleSidebar}
          className="text-white hover:bg-white/20"
        >
          <Menu className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-bold text-white">Chat Wizard</h1>
      </div>
      
      <div className="flex items-center gap-4">
        <ModelSelector 
          selectedModel={selectedModel}
          onModelChange={onModelChange}
        />
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/20"
        >
          <Settings className="h-5 w-5" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/20"
        >
          <User className="h-5 w-5" />
        </Button>
      </div>
    </header>
  );
};
