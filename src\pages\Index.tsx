
import React from 'react';
import { ChatInterface } from '@/components/chat/ChatInterface';
import { Sidebar } from '@/components/layout/Sidebar';
import { Header } from '@/components/layout/Header';
import { useState, useEffect } from 'react';
import { usePuterAuth } from '@/hooks/usePuterAuth';

const Index = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedModel, setSelectedModel] = useState('gpt-4o-mini');
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const { autoSignIn } = usePuterAuth();

  useEffect(() => {
    // Automatically attempt to sign in when the app loads
    autoSignIn();
  }, [autoSignIn]);

  const handleNewChat = () => {
    const newChatId = `chat_${Date.now()}`;
    setCurrentChatId(newChatId);
  };

  const handleSelectChat = (chatId: string) => {
    setCurrentChatId(chatId);
  };

  const handleNewMessage = (message: any) => {
    // This can be used to update the sidebar with new messages
    console.log('New message:', message);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="flex h-screen">
        <Sidebar 
          isOpen={sidebarOpen} 
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          onNewChat={handleNewChat}
          onSelectChat={handleSelectChat}
          currentChatId={currentChatId}
        />
        <div className="flex-1 flex flex-col">
          <Header 
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
          />
          <main className="flex-1 overflow-hidden">
            <ChatInterface 
              selectedModel={selectedModel}
              currentChatId={currentChatId}
              onNewMessage={handleNewMessage}
            />
          </main>
        </div>
      </div>
    </div>
  );
};

export default Index;
