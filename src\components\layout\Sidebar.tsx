
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus } from 'lucide-react';
import { ChatHistory } from '@/components/chat/ChatHistory';
import { UserProfile } from '@/components/user/UserProfile';
import { usePuterStorage } from '@/hooks/usePuterStorage';
import { usePuterAuth } from '@/hooks/usePuterAuth';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
  currentChatId: string | null;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onToggle, 
  onNewChat, 
  onSelectChat, 
  currentChatId 
}) => {
  const [chats, setChats] = useState<any[]>([]);
  const { listChats, deleteChat } = usePuterStorage();
  const { isSignedIn } = usePuterAuth();

  const loadChats = async () => {
    if (isSignedIn) {
      try {
        const chatList = await listChats();
        setChats(chatList.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()));
      } catch (error) {
        console.error('Failed to load chats:', error);
      }
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    try {
      await deleteChat(chatId);
      setChats(chats.filter(chat => chat.id !== chatId));
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  };

  useEffect(() => {
    loadChats();
  }, [isSignedIn]);

  if (!isOpen) {
    return (
      <div className="w-0 transition-all duration-300 overflow-hidden" />
    );
  }

  return (
    <div className="w-80 bg-white/5 backdrop-blur-lg border-r border-white/20 flex flex-col transition-all duration-300">
      <div className="p-4 border-b border-white/20">
        <Button 
          onClick={onNewChat}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Chat
        </Button>
      </div>
      
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium text-white/70 mb-3">Recent Chats</h3>
            <ChatHistory 
              chats={chats}
              currentChatId={currentChatId}
              onSelectChat={onSelectChat}
              onDeleteChat={handleDeleteChat}
            />
          </div>
          
          <div className="border-t border-white/20 pt-6">
            <h3 className="text-sm font-medium text-white/70 mb-3">Account</h3>
            <UserProfile />
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};
