import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Molecular3D } from './Molecular3D';
import { ChemicalReaction } from './ChemicalReaction';
import { MOLECULES, REACTIONS, MolecularUtils } from '@/lib/molecularData';
import { Atom, Beaker, Zap, Settings, Info } from 'lucide-react';

export const MolecularPlayground: React.FC = () => {
  const [selectedMolecule, setSelectedMolecule] = useState('water');
  const [selectedReaction, setSelectedReaction] = useState('carbonicAcidFormation');
  const [activeTab, setActiveTab] = useState('molecules');

  const moleculeOptions = Object.entries(MOLECULES).map(([key, molecule]) => ({
    value: key,
    label: `${molecule.name} (${molecule.formula})`,
    molecule
  }));

  const reactionOptions = Object.entries(REACTIONS).map(([key, reaction]) => ({
    value: key,
    label: reaction.name,
    reaction
  }));

  const currentMolecule = MOLECULES[selectedMolecule];
  const currentReaction = REACTIONS[selectedReaction];

  return (
    <Card className="w-full max-w-7xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <Atom className="h-6 w-6 text-blue-400" />
          3D Molecular Playground
          <Badge variant="secondary" className="ml-2">Interactive</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700">
            <TabsTrigger value="molecules" className="flex items-center gap-2">
              <Atom className="h-4 w-4" />
              Molecules
            </TabsTrigger>
            <TabsTrigger value="reactions" className="flex items-center gap-2">
              <Beaker className="h-4 w-4" />
              Reactions
            </TabsTrigger>
            <TabsTrigger value="builder" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Builder
            </TabsTrigger>
            <TabsTrigger value="info" className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Info
            </TabsTrigger>
          </TabsList>

          {/* Individual Molecules Tab */}
          <TabsContent value="molecules" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <label className="text-white font-medium">Select Molecule:</label>
                <Select value={selectedMolecule} onValueChange={setSelectedMolecule}>
                  <SelectTrigger className="w-64 bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {moleculeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {currentMolecule && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 3D Visualization */}
                  <div className="lg:col-span-2">
                    <Molecular3D 
                      molecules={[currentMolecule]}
                      showControls={true}
                    />
                  </div>

                  {/* Molecule Information */}
                  <div className="space-y-4">
                    <Card className="bg-slate-700/50 border-slate-600">
                      <CardHeader>
                        <CardTitle className="text-white text-lg">
                          {currentMolecule.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <span className="text-slate-300 font-medium">Formula:</span>
                          <span className="text-white ml-2">{currentMolecule.formula}</span>
                        </div>
                        {currentMolecule.molecularWeight && (
                          <div>
                            <span className="text-slate-300 font-medium">Molecular Weight:</span>
                            <span className="text-white ml-2">{currentMolecule.molecularWeight} g/mol</span>
                          </div>
                        )}
                        {currentMolecule.boilingPoint && (
                          <div>
                            <span className="text-slate-300 font-medium">Boiling Point:</span>
                            <span className="text-white ml-2">{currentMolecule.boilingPoint}°C</span>
                          </div>
                        )}
                        {currentMolecule.meltingPoint && (
                          <div>
                            <span className="text-slate-300 font-medium">Melting Point:</span>
                            <span className="text-white ml-2">{currentMolecule.meltingPoint}°C</span>
                          </div>
                        )}
                        <div>
                          <span className="text-slate-300 font-medium">Atoms:</span>
                          <span className="text-white ml-2">{currentMolecule.atoms.length}</span>
                        </div>
                        <div>
                          <span className="text-slate-300 font-medium">Bonds:</span>
                          <span className="text-white ml-2">{currentMolecule.bonds.length}</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Atom Details */}
                    <Card className="bg-slate-700/50 border-slate-600">
                      <CardHeader>
                        <CardTitle className="text-white text-lg">Atomic Composition</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {currentMolecule.atoms.map((atom, index) => (
                            <div key={atom.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-4 h-4 rounded-full border border-white/20"
                                  style={{ backgroundColor: atom.color }}
                                />
                                <span className="text-white">{atom.element}</span>
                              </div>
                              <span className="text-slate-400 text-sm">
                                ({atom.x.toFixed(0)}, {atom.y.toFixed(0)}, {atom.z.toFixed(0)})
                              </span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Chemical Reactions Tab */}
          <TabsContent value="reactions" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <label className="text-white font-medium">Select Reaction:</label>
                <Select value={selectedReaction} onValueChange={setSelectedReaction}>
                  <SelectTrigger className="w-64 bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {reactionOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <ChemicalReaction 
                reactionId={selectedReaction}
                autoPlay={true}
                showControls={true}
              />

              {currentReaction && (
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-white">Reaction Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-slate-300 font-medium">Equation:</span>
                      <span className="text-white ml-2 font-mono">{currentReaction.equation}</span>
                    </div>
                    <div>
                      <span className="text-slate-300 font-medium">Type:</span>
                      <Badge variant="outline" className="ml-2">
                        {currentReaction.type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-slate-300 font-medium">Energy Change:</span>
                      <span className={`ml-2 ${currentReaction.energyChange < 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {currentReaction.energyChange} kJ/mol
                        {currentReaction.energyChange < 0 ? ' (Exothermic)' : ' (Endothermic)'}
                      </span>
                    </div>
                    <div>
                      <span className="text-slate-300 font-medium">Description:</span>
                      <p className="text-white mt-1">{currentReaction.description}</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Molecule Builder Tab */}
          <TabsContent value="builder" className="mt-6">
            <div className="space-y-6">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white">Molecule Builder</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <Settings className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                    <h3 className="text-xl text-white mb-2">Coming Soon!</h3>
                    <p className="text-slate-400">
                      Interactive molecule builder where you can create custom molecular structures
                      by adding atoms and bonds in 3D space.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Information Tab */}
          <TabsContent value="info" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white">🧬 About 3D Molecular Visualization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-slate-300">
                  <p>
                    This interactive 3D molecular playground allows you to explore chemical structures
                    and reactions in three-dimensional space.
                  </p>
                  <h4 className="text-white font-semibold">Features:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Real-time 3D rotation and perspective</li>
                    <li>Accurate atomic colors and sizes</li>
                    <li>Chemical bond visualization</li>
                    <li>Animated reaction pathways</li>
                    <li>Interactive controls and parameters</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white">⚗️ Chemical Reactions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-slate-300">
                  <p>
                    Watch molecules interact and transform through animated chemical reactions
                    with realistic molecular motion and energy changes.
                  </p>
                  <h4 className="text-white font-semibold">Reaction Types:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Synthesis reactions</li>
                    <li>Decomposition reactions</li>
                    <li>Combustion reactions</li>
                    <li>Replacement reactions</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white">🎮 Controls</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-slate-300">
                  <h4 className="text-white font-semibold">Animation Controls:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Play/Pause animations</li>
                    <li>Adjust animation speed</li>
                    <li>Control zoom and rotation</li>
                    <li>Toggle bonds and labels</li>
                    <li>Export SVG animations</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white">🔬 Educational Value</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-slate-300">
                  <p>
                    Perfect for chemistry education, research visualization, and understanding
                    molecular behavior at the atomic level.
                  </p>
                  <h4 className="text-white font-semibold">Learn About:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Molecular geometry</li>
                    <li>Chemical bonding</li>
                    <li>Reaction mechanisms</li>
                    <li>Energy changes</li>
                    <li>3D molecular structure</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
