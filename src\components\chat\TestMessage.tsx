import React from 'react';
import { MessageRenderer } from './MessageRenderer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const TestMessage: React.FC = () => {
  const testContent = `# Live Preview Test

Here's a simple HTML example that should show a live preview:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        .button:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🎉 Hello World!</h1>
        <p>This is a test of the live preview functionality.</p>
        <button class="button" onclick="alert('Button clicked!')">Click Me</button>
    </div>
</body>
</html>
\`\`\`

And here's a CSS example:

\`\`\`css
.animated-box {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}
\`\`\`

And a JavaScript example:

\`\`\`javascript
function createColorfulDiv() {
  const div = document.createElement('div');
  div.style.width = '100px';
  div.style.height = '100px';
  div.style.backgroundColor = \`hsl(\${Math.random() * 360}, 70%, 50%)\`;
  div.style.margin = '10px';
  div.style.borderRadius = '10px';
  div.style.display = 'inline-block';
  div.style.cursor = 'pointer';
  
  div.addEventListener('click', () => {
    div.style.backgroundColor = \`hsl(\${Math.random() * 360}, 70%, 50%)\`;
  });
  
  return div;
}

// Create some colorful divs
for (let i = 0; i < 5; i++) {
  document.body.appendChild(createColorfulDiv());
}
\`\`\`

If the live preview is working correctly, you should see interactive previews for these code blocks!`;

  return (
    <Card className="w-full max-w-4xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="text-white">Live Preview Test</CardTitle>
      </CardHeader>
      <CardContent>
        <MessageRenderer content={testContent} />
      </CardContent>
    </Card>
  );
};
