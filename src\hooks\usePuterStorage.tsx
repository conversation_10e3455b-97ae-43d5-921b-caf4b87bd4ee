
import { useState, useCallback } from 'react';

export const usePuterStorage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const saveChat = useCallback(async (chatId: string, chatData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const key = `chat_${chatId}`;
      await window.puter.kv.set(key, JSON.stringify(chatData));
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save chat';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadChat = useCallback(async (chatId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const key = `chat_${chatId}`;
      const data = await window.puter.kv.get(key);
      return data ? JSON.parse(data) : null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load chat';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const listChats = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const keys = await window.puter.kv.list('chat_*', true) as Array<{ key: string; value: string }>;
      return keys.map(({ key, value }) => ({
        id: key.replace('chat_', ''),
        ...JSON.parse(value),
      }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to list chats';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteChat = useCallback(async (chatId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const key = `chat_${chatId}`;
      await window.puter.kv.del(key);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete chat';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    saveChat,
    loadChat,
    listChats,
    deleteChat,
    isLoading,
    error,
  };
};
