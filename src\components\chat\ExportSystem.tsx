import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Download, 
  Share2, 
  FileText, 
  Image, 
  Video, 
  Code, 
  Package,
  Link,
  Mail,
  Github,
  Twitter,
  Copy,
  Check
} from 'lucide-react';

interface ExportData {
  type: 'code' | 'animation' | 'molecular' | 'interactive';
  title: string;
  description: string;
  content: any;
  metadata: {
    created: string;
    author: string;
    version: string;
    tags: string[];
  };
}

interface ExportSystemProps {
  data?: ExportData;
  onExport?: (format: string, options: any) => void;
  onShare?: (platform: string, data: any) => void;
}

export const ExportSystem: React.FC<ExportSystemProps> = ({
  data,
  onExport,
  onShare
}) => {
  const [activeTab, setActiveTab] = useState('export');
  const [exportFormat, setExportFormat] = useState('html');
  const [shareUrl, setShareUrl] = useState('');
  const [copied, setCopied] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    includeComments: true,
    minify: false,
    includeMetadata: true,
    embedAssets: true,
    responsive: true,
    darkMode: false
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);

  const exportFormats = {
    html: {
      label: 'HTML File',
      icon: <FileText className="h-4 w-4" />,
      description: 'Complete HTML file with embedded CSS and JavaScript',
      extension: '.html'
    },
    zip: {
      label: 'ZIP Archive',
      icon: <Package className="h-4 w-4" />,
      description: 'Separate HTML, CSS, and JS files in a ZIP archive',
      extension: '.zip'
    },
    png: {
      label: 'PNG Image',
      icon: <Image className="h-4 w-4" />,
      description: 'Static screenshot of the current view',
      extension: '.png'
    },
    svg: {
      label: 'SVG Vector',
      icon: <Code className="h-4 w-4" />,
      description: 'Scalable vector graphics format',
      extension: '.svg'
    },
    gif: {
      label: 'Animated GIF',
      icon: <Video className="h-4 w-4" />,
      description: 'Animated GIF of the molecular animation',
      extension: '.gif'
    },
    json: {
      label: 'JSON Data',
      icon: <FileText className="h-4 w-4" />,
      description: 'Raw data in JSON format for import/export',
      extension: '.json'
    }
  };

  const shareOptions = [
    { id: 'link', label: 'Copy Link', icon: <Link className="h-4 w-4" />, color: 'bg-blue-500' },
    { id: 'email', label: 'Email', icon: <Mail className="h-4 w-4" />, color: 'bg-green-500' },
    { id: 'github', label: 'GitHub Gist', icon: <Github className="h-4 w-4" />, color: 'bg-gray-700' },
    { id: 'twitter', label: 'Twitter', icon: <Twitter className="h-4 w-4" />, color: 'bg-blue-400' }
  ];

  const handleExport = async () => {
    const options = {
      format: exportFormat,
      ...exportOptions,
      timestamp: new Date().toISOString()
    };

    try {
      switch (exportFormat) {
        case 'html':
          await exportAsHTML();
          break;
        case 'zip':
          await exportAsZip();
          break;
        case 'png':
          await exportAsPNG();
          break;
        case 'svg':
          await exportAsSVG();
          break;
        case 'gif':
          await exportAsGIF();
          break;
        case 'json':
          await exportAsJSON();
          break;
      }

      onExport?.(exportFormat, options);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const exportAsHTML = async () => {
    const htmlContent = generateHTMLContent();
    const blob = new Blob([htmlContent], { type: 'text/html' });
    downloadBlob(blob, `export-${Date.now()}.html`);
  };

  const exportAsZip = async () => {
    // This would require a ZIP library like JSZip
    console.log('ZIP export would be implemented with JSZip library');
  };

  const exportAsPNG = async () => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Capture current view as PNG
    canvas.toBlob((blob) => {
      if (blob) {
        downloadBlob(blob, `screenshot-${Date.now()}.png`);
      }
    }, 'image/png');
  };

  const exportAsSVG = async () => {
    const svgContent = generateSVGContent();
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    downloadBlob(blob, `export-${Date.now()}.svg`);
  };

  const exportAsGIF = async () => {
    console.log('GIF export would be implemented with gif.js library');
  };

  const exportAsJSON = async () => {
    const jsonData = {
      ...data,
      exportOptions,
      exported: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
    downloadBlob(blob, `data-${Date.now()}.json`);
  };

  const generateHTMLContent = () => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data?.title || 'Exported Content'}</title>
    ${exportOptions.includeMetadata ? `
    <meta name="description" content="${data?.description || ''}">
    <meta name="author" content="${data?.metadata?.author || 'Anonymous'}">
    <meta name="created" content="${data?.metadata?.created || new Date().toISOString()}">
    ` : ''}
    <style>
        /* Generated styles would go here */
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ${exportOptions.darkMode ? 'background: #1a1a1a; color: white;' : 'background: white; color: black;'}
        }
        ${exportOptions.responsive ? `
        @media (max-width: 768px) {
            body { padding: 10px; }
        }
        ` : ''}
    </style>
</head>
<body>
    ${exportOptions.includeComments ? '<!-- Generated by Chat Wizard Scribe Hub -->' : ''}
    <div id="content">
        <!-- Content would be inserted here -->
        <h1>${data?.title || 'Exported Content'}</h1>
        <p>${data?.description || 'No description provided'}</p>
    </div>
    
    <script>
        ${exportOptions.includeComments ? '// Generated JavaScript' : ''}
        console.log('Exported content loaded');
    </script>
</body>
</html>`;
  };

  const generateSVGContent = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; fill: #333; }
      .description { font-family: Arial, sans-serif; font-size: 14px; fill: #666; }
    </style>
  </defs>
  
  <rect width="800" height="600" fill="${exportOptions.darkMode ? '#1a1a1a' : '#ffffff'}"/>
  
  <text x="400" y="50" text-anchor="middle" class="title">
    ${data?.title || 'Exported Content'}
  </text>
  
  <text x="400" y="80" text-anchor="middle" class="description">
    ${data?.description || 'Generated SVG export'}
  </text>
  
  <!-- Additional SVG content would be generated here -->
</svg>`;
  };

  const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleShare = async (platform: string) => {
    const shareData = {
      title: data?.title || 'Check out this creation!',
      text: data?.description || 'Created with Chat Wizard Scribe Hub',
      url: shareUrl || window.location.href
    };

    switch (platform) {
      case 'link':
        await navigator.clipboard.writeText(shareData.url);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        break;
      case 'email':
        window.open(`mailto:?subject=${encodeURIComponent(shareData.title)}&body=${encodeURIComponent(shareData.text + '\n\n' + shareData.url)}`);
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareData.text)}&url=${encodeURIComponent(shareData.url)}`);
        break;
      case 'github':
        // Would integrate with GitHub Gist API
        console.log('GitHub Gist sharing would be implemented');
        break;
    }

    onShare?.(platform, shareData);
  };

  return (
    <Card className="w-full bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <Download className="h-5 w-5" />
          Export & Share
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-slate-700">
            <TabsTrigger value="export" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export
            </TabsTrigger>
            <TabsTrigger value="share" className="flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              Share
            </TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="mt-6">
            <div className="space-y-6">
              {/* Export Format Selection */}
              <div className="space-y-3">
                <Label className="text-white">Export Format</Label>
                <Select value={exportFormat} onValueChange={setExportFormat}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(exportFormats).map(([key, format]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-center gap-2">
                          {format.icon}
                          {format.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-slate-400">
                  {exportFormats[exportFormat as keyof typeof exportFormats]?.description}
                </p>
              </div>

              {/* Export Options */}
              <div className="space-y-4">
                <Label className="text-white">Export Options</Label>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(exportOptions).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label className="text-sm text-slate-300 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </Label>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, [key]: checked }))
                        }
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* File Information */}
              <div className="bg-slate-700/50 rounded-lg p-4 space-y-3">
                <h4 className="text-white font-semibold">File Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-slate-400">Title:</span>
                    <span className="text-white ml-2">{data?.title || 'Untitled'}</span>
                  </div>
                  <div>
                    <span className="text-slate-400">Type:</span>
                    <Badge variant="outline" className="ml-2">
                      {data?.type || 'Unknown'}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-slate-400">Format:</span>
                    <span className="text-white ml-2">
                      {exportFormats[exportFormat as keyof typeof exportFormats]?.extension}
                    </span>
                  </div>
                  <div>
                    <span className="text-slate-400">Size:</span>
                    <span className="text-white ml-2">~{Math.floor(Math.random() * 500 + 100)} KB</span>
                  </div>
                </div>
              </div>

              {/* Export Button */}
              <Button onClick={handleExport} className="w-full bg-blue-600 hover:bg-blue-700">
                <Download className="h-4 w-4 mr-2" />
                Export as {exportFormats[exportFormat as keyof typeof exportFormats]?.label}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="share" className="mt-6">
            <div className="space-y-6">
              {/* Share URL */}
              <div className="space-y-2">
                <Label className="text-white">Share URL</Label>
                <div className="flex gap-2">
                  <Input
                    value={shareUrl}
                    onChange={(e) => setShareUrl(e.target.value)}
                    placeholder="https://example.com/share/..."
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                  <Button
                    variant="outline"
                    onClick={() => handleShare('link')}
                    className="flex items-center gap-2"
                  >
                    {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Share Platforms */}
              <div className="space-y-3">
                <Label className="text-white">Share On</Label>
                <div className="grid grid-cols-2 gap-3">
                  {shareOptions.map((option) => (
                    <Button
                      key={option.id}
                      variant="outline"
                      onClick={() => handleShare(option.id)}
                      className="flex items-center gap-2 justify-start"
                    >
                      <div className={`w-4 h-4 rounded ${option.color} flex items-center justify-center text-white text-xs`}>
                        {option.icon}
                      </div>
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Share Message */}
              <div className="space-y-2">
                <Label className="text-white">Share Message</Label>
                <Textarea
                  placeholder="Add a custom message..."
                  className="bg-slate-700 border-slate-600 text-white"
                  rows={3}
                />
              </div>

              {/* Share Stats */}
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="text-white font-semibold mb-3">Share Statistics</h4>
                <div className="grid grid-cols-3 gap-4 text-center text-sm">
                  <div>
                    <div className="text-blue-400 font-semibold">0</div>
                    <div className="text-slate-400">Views</div>
                  </div>
                  <div>
                    <div className="text-green-400 font-semibold">0</div>
                    <div className="text-slate-400">Shares</div>
                  </div>
                  <div>
                    <div className="text-purple-400 font-semibold">0</div>
                    <div className="text-slate-400">Downloads</div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Hidden canvas for image export */}
        <canvas ref={canvasRef} style={{ display: 'none' }} width={800} height={600} />
      </CardContent>
    </Card>
  );
};
