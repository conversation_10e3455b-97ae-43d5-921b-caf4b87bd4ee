import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  Search, 
  Code, 
  Zap, 
  Atom, 
  Settings,
  Download,
  ExternalLink,
  ChevronRight,
  Star,
  Users,
  GitBranch,
  MessageSquare
} from 'lucide-react';

interface DocumentationSection {
  id: string;
  title: string;
  description: string;
  category: 'getting-started' | 'features' | 'api' | 'examples' | 'troubleshooting';
  content: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export const Documentation: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSection, setSelectedSection] = useState<string | null>(null);

  const documentationSections: DocumentationSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Quick start guide to using Chat Wizard Scribe Hub',
      category: 'getting-started',
      difficulty: 'beginner',
      tags: ['setup', 'basics', 'introduction'],
      content: `# Getting Started with Chat Wizard Scribe Hub

Welcome to Chat Wizard Scribe Hub! This comprehensive guide will help you get started with our advanced interactive chat system.

## Quick Start

1. **Open the Application**: Navigate to the main interface
2. **Explore Demo Features**: Click "See Features Demo" to explore capabilities
3. **Try Interactive Examples**: Test live code previews and molecular animations
4. **Customize Settings**: Use advanced controls to fine-tune your experience

## Key Features

- **Enhanced Message Rendering**: Rich markdown with syntax highlighting
- **Live Code Previews**: Real-time HTML, CSS, and JavaScript rendering
- **3D Molecular Animations**: Interactive chemical visualizations
- **Advanced Parameter Controls**: Fine-tune animations and effects
- **Export & Share**: Multiple formats and sharing options

## Navigation

The interface is organized into several main sections:
- **Chat Interface**: Main conversation area
- **Demo Features**: Showcase of all capabilities
- **Interactive Playground**: Advanced feature testing
- **Documentation**: This help system`
    },
    {
      id: 'live-preview',
      title: 'Live Code Preview',
      description: 'How to use the real-time code editor and preview system',
      category: 'features',
      difficulty: 'beginner',
      tags: ['code', 'preview', 'html', 'css', 'javascript'],
      content: `# Live Code Preview System

The live code preview system allows you to write HTML, CSS, and JavaScript with real-time rendering.

## Features

- **Multi-language Support**: HTML, CSS, and JavaScript
- **Real-time Updates**: See changes as you type
- **Syntax Highlighting**: Color-coded syntax for better readability
- **Error Detection**: Automatic error highlighting
- **Export Options**: Download complete HTML files

## Usage

1. **Select Language Tab**: Choose HTML, CSS, or JavaScript
2. **Write Code**: Type your code in the editor
3. **View Preview**: See real-time results in the preview pane
4. **Export**: Download your creation as an HTML file

## Example

\`\`\`html
<div class="demo">
  <h1>Hello World!</h1>
  <p>This is a live preview example.</p>
</div>
\`\`\`

\`\`\`css
.demo {
  padding: 20px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 10px;
}
\`\`\``
    },
    {
      id: 'molecular-animations',
      title: '3D Molecular Animations',
      description: 'Creating and controlling 3D molecular visualizations',
      category: 'features',
      difficulty: 'intermediate',
      tags: ['3d', 'molecular', 'chemistry', 'animations'],
      content: `# 3D Molecular Animation System

Create stunning 3D molecular visualizations and chemical reaction animations.

## Available Molecules

- **Water (H₂O)**: Bent molecular geometry
- **Carbon Dioxide (CO₂)**: Linear structure
- **Methane (CH₄)**: Tetrahedral geometry
- **Ammonia (NH₃)**: Trigonal pyramidal
- **Benzene (C₆H₆)**: Aromatic ring structure
- **Carbonic Acid (H₂CO₃)**: Complex acid structure

## Chemical Reactions

- **Water Formation**: 2H₂ + O₂ → 2H₂O
- **Carbonic Acid Formation**: H₂O + CO₂ → H₂CO₃
- **Methane Combustion**: CH₄ + 2O₂ → CO₂ + 2H₂O

## Controls

- **Animation Speed**: Adjust from 0.1x to 3x speed
- **Zoom**: Control camera distance
- **Rotation**: Manual rotation controls
- **Visual Options**: Toggle bonds, labels, and effects

## Scientific Accuracy

All molecular structures are based on:
- VSEPR theory for geometry
- CPK color convention for atoms
- Experimental bond lengths and angles
- Thermodynamic data for reactions`
    },
    {
      id: 'advanced-controls',
      title: 'Advanced Parameter Controls',
      description: 'Fine-tuning animations and visual effects',
      category: 'features',
      difficulty: 'advanced',
      tags: ['controls', 'parameters', 'customization'],
      content: `# Advanced Parameter Controls

Fine-tune every aspect of your animations and visualizations with comprehensive parameter controls.

## Parameter Categories

### Animation Controls
- **Speed**: Animation playback speed (0.1x - 5x)
- **Duration**: Total animation length
- **Easing**: Animation timing functions
- **Loop**: Continuous playback options

### Visual Controls
- **Zoom**: Camera zoom level (0.5x - 3x)
- **Rotation**: X, Y, Z axis rotation
- **Perspective**: 3D perspective distance
- **Display Options**: Bonds, labels, particles

### Effects Controls
- **Glow Intensity**: Atom glow effects
- **Shadow Blur**: Drop shadow intensity
- **Particle Count**: Reaction particle effects
- **Color Schemes**: CPK, Jmol, RasMol, Custom

## Presets

- **Educational**: Optimized for learning
- **Presentation**: High-impact visuals
- **Scientific**: Accurate representations
- **Artistic**: Creative styling

## Performance Monitoring

Real-time performance metrics:
- Frame rate (FPS)
- Memory usage
- CPU utilization
- Render time`
    },
    {
      id: 'export-share',
      title: 'Export & Share System',
      description: 'Saving and sharing your creations',
      category: 'features',
      difficulty: 'beginner',
      tags: ['export', 'share', 'download'],
      content: `# Export & Share System

Save your creations in multiple formats and share them with others.

## Export Formats

- **HTML**: Complete web page with embedded styles
- **ZIP**: Separate HTML, CSS, JS files
- **PNG**: Static screenshot
- **SVG**: Scalable vector graphics
- **GIF**: Animated sequences
- **JSON**: Raw data for import/export

## Export Options

- **Include Comments**: Add code documentation
- **Minify Code**: Compress for production
- **Include Metadata**: Author and creation info
- **Embed Assets**: Self-contained files
- **Responsive Design**: Mobile-friendly output

## Sharing Platforms

- **Copy Link**: Direct URL sharing
- **Email**: Send via email client
- **GitHub Gist**: Code snippet sharing
- **Twitter**: Social media sharing

## File Management

- Custom filenames
- Automatic timestamps
- Size optimization
- Format validation`
    },
    {
      id: 'troubleshooting',
      title: 'Troubleshooting Guide',
      description: 'Common issues and solutions',
      category: 'troubleshooting',
      difficulty: 'beginner',
      tags: ['help', 'issues', 'solutions'],
      content: `# Troubleshooting Guide

Common issues and their solutions.

## Live Preview Not Working

**Problem**: Code preview not updating
**Solutions**:
- Check if auto-run is enabled
- Verify code syntax is correct
- Try refreshing the preview manually
- Check browser console for errors

## Performance Issues

**Problem**: Slow animations or lag
**Solutions**:
- Reduce animation speed
- Lower particle count
- Disable glow effects
- Close other browser tabs

## Export Problems

**Problem**: Export not working
**Solutions**:
- Check browser download permissions
- Verify file format is supported
- Try a different export format
- Clear browser cache

## Browser Compatibility

**Supported Browsers**:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Getting Help

- Check this documentation
- Review example code
- Test with simple examples
- Report bugs via GitHub issues`
    }
  ];

  const filteredSections = documentationSections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getDifficultyColor = (difficulty: DocumentationSection['difficulty']) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-500';
      case 'intermediate':
        return 'bg-yellow-500';
      case 'advanced':
        return 'bg-red-500';
    }
  };

  const getCategoryIcon = (category: DocumentationSection['category']) => {
    switch (category) {
      case 'getting-started':
        return <Star className="h-4 w-4" />;
      case 'features':
        return <Zap className="h-4 w-4" />;
      case 'api':
        return <Code className="h-4 w-4" />;
      case 'examples':
        return <BookOpen className="h-4 w-4" />;
      case 'troubleshooting':
        return <Settings className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full max-w-6xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <BookOpen className="h-6 w-6 text-blue-400" />
            Documentation
            <Badge variant="secondary" className="ml-2">v1.0.0</Badge>
          </div>
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              GitHub
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search documentation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-slate-700 border-slate-600 text-white"
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-700">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
              <TabsTrigger value="support">Support</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Section List */}
                <div className="lg:col-span-1 space-y-3">
                  <h3 className="text-white font-semibold">Documentation Sections</h3>
                  {filteredSections.map((section) => (
                    <div
                      key={section.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedSection === section.id
                          ? 'bg-blue-500/20 border border-blue-500/50'
                          : 'bg-slate-700/50 hover:bg-slate-700/70'
                      }`}
                      onClick={() => setSelectedSection(section.id)}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        {getCategoryIcon(section.category)}
                        <span className="text-white font-medium text-sm">{section.title}</span>
                        <ChevronRight className="h-3 w-3 text-slate-400 ml-auto" />
                      </div>
                      <p className="text-xs text-slate-400">{section.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className={`w-2 h-2 rounded-full ${getDifficultyColor(section.difficulty)}`}></div>
                        <span className="text-xs text-slate-400 capitalize">{section.difficulty}</span>
                        <div className="flex gap-1 ml-auto">
                          {section.tags.slice(0, 2).map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Content Area */}
                <div className="lg:col-span-2">
                  {selectedSection ? (
                    <div className="bg-slate-700/50 rounded-lg p-6">
                      <div className="prose prose-invert max-w-none">
                        <pre className="whitespace-pre-wrap text-sm text-slate-300 leading-relaxed">
                          {filteredSections.find(s => s.id === selectedSection)?.content}
                        </pre>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-slate-700/50 rounded-lg p-6 text-center">
                      <BookOpen className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                      <h3 className="text-white font-semibold mb-2">Welcome to the Documentation</h3>
                      <p className="text-slate-400">
                        Select a section from the left to view detailed documentation.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="features" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredSections.filter(s => s.category === 'features').map((section) => (
                  <div key={section.id} className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      {getCategoryIcon(section.category)}
                      <h4 className="text-white font-medium">{section.title}</h4>
                      <div className={`w-2 h-2 rounded-full ${getDifficultyColor(section.difficulty)} ml-auto`}></div>
                    </div>
                    <p className="text-sm text-slate-400 mb-3">{section.description}</p>
                    <div className="flex gap-1 flex-wrap">
                      {section.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="examples" className="mt-6">
              <div className="space-y-4">
                <div className="bg-slate-700/50 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-4">Code Examples</h3>
                  <div className="space-y-3">
                    <div className="text-sm text-slate-300">
                      • Basic HTML with live preview
                    </div>
                    <div className="text-sm text-slate-300">
                      • Interactive CSS animations
                    </div>
                    <div className="text-sm text-slate-300">
                      • 3D molecular visualizations
                    </div>
                    <div className="text-sm text-slate-300">
                      • Advanced parameter controls
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="support" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-slate-700/50 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Get Help
                  </h3>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      <GitBranch className="h-4 w-4 mr-2" />
                      GitHub Issues
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Users className="h-4 w-4 mr-2" />
                      Community Forum
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Contact Support
                    </Button>
                  </div>
                </div>

                <div className="bg-slate-700/50 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-4">Quick Links</h3>
                  <div className="space-y-2 text-sm">
                    <div className="text-slate-300">• Version: 1.0.0</div>
                    <div className="text-slate-300">• Last Updated: {new Date().toLocaleDateString()}</div>
                    <div className="text-slate-300">• License: MIT</div>
                    <div className="text-slate-300">• Contributors: 1</div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
};
