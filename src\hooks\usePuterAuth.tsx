
import { useState, useEffect, useCallback } from 'react';

interface PuterUser {
  uuid: string;
  username: string;
  email_confirmed: boolean;
}

export const usePuterAuth = () => {
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [user, setUser] = useState<PuterUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuthStatus = useCallback(async () => {
    try {
      if (window.puter?.auth) {
        const signedIn = window.puter.auth.isSignedIn();
        setIsSignedIn(signedIn);
        
        if (signedIn) {
          const userData = await window.puter.auth.getUser();
          setUser(userData);
        }
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const autoSignIn = useCallback(async () => {
    try {
      if (window.puter?.auth && !window.puter.auth.isSignedIn()) {
        setIsLoading(true);
        // Auto-authenticate without popup by using silent authentication
        await window.puter.auth.signIn();
        await checkAuthStatus();
      }
    } catch (error) {
      console.error('Auto sign in error:', error);
      // If auto sign-in fails, just continue without authentication
      setIsLoading(false);
    }
  }, [checkAuthStatus]);

  const signIn = useCallback(async () => {
    try {
      setIsLoading(true);
      await window.puter.auth.signIn();
      await checkAuthStatus();
    } catch (error) {
      console.error('Sign in error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [checkAuthStatus]);

  useEffect(() => {
    // Wait for puter to be available
    const checkPuter = () => {
      if (window.puter) {
        checkAuthStatus().then(() => {
          // Automatically attempt sign-in if not already signed in
          if (!window.puter.auth.isSignedIn()) {
            autoSignIn();
          }
        });
      } else {
        setTimeout(checkPuter, 100);
      }
    };
    checkPuter();
  }, [checkAuthStatus, autoSignIn]);

  return {
    isSignedIn,
    user,
    isLoading,
    signIn,
    checkAuthStatus,
    autoSignIn,
  };
};
