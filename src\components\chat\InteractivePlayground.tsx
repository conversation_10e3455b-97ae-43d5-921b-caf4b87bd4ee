import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { AdvancedControls } from './AdvancedControls';
import { CodeEditor } from './CodeEditor';
import { ExportSystem } from './ExportSystem';
import { MolecularPlayground } from './MolecularPlayground';
import { 
  Zap, 
  Code, 
  Download, 
  Atom, 
  Settings,
  Palette,
  Play,
  Layers
} from 'lucide-react';

export const InteractivePlayground: React.FC = () => {
  const [activeTab, setActiveTab] = useState('molecular');
  const [parameters, setParameters] = useState<any>({});
  const [codeContent, setCodeContent] = useState({
    html: '',
    css: '',
    javascript: ''
  });

  const handleParameterChange = (id: string, value: any) => {
    setParameters(prev => ({ ...prev, [id]: value }));
    console.log(`Parameter ${id} changed to:`, value);
  };

  const handleCodeChange = (language: string, code: string) => {
    setCodeContent(prev => ({ ...prev, [language]: code }));
    console.log(`${language} code updated:`, code.length, 'characters');
  };

  const handleExport = (format: string, options: any) => {
    console.log('Exporting as:', format, 'with options:', options);
  };

  const handleShare = (platform: string, data: any) => {
    console.log('Sharing on:', platform, 'with data:', data);
  };

  const handlePresetLoad = (preset: string) => {
    console.log('Loading preset:', preset);
    // Load preset configurations
    switch (preset) {
      case 'educational':
        setParameters({
          speed: 0.5,
          showLabels: true,
          showBonds: true,
          glowIntensity: 0.3
        });
        break;
      case 'presentation':
        setParameters({
          speed: 1.5,
          zoom: 1.2,
          glowIntensity: 0.8,
          enableParticles: true
        });
        break;
      case 'scientific':
        setParameters({
          speed: 1,
          showLabels: true,
          showBonds: true,
          colorScheme: 'cpk'
        });
        break;
      case 'artistic':
        setParameters({
          speed: 2,
          glowIntensity: 1.5,
          enableParticles: true,
          colorScheme: 'custom'
        });
        break;
    }
  };

  const exportData = {
    type: 'interactive' as const,
    title: 'Interactive Playground Creation',
    description: 'A comprehensive interactive experience created with advanced controls',
    content: {
      parameters,
      code: codeContent,
      activeTab
    },
    metadata: {
      created: new Date().toISOString(),
      author: 'Chat Wizard User',
      version: '1.0.0',
      tags: ['interactive', 'molecular', 'code', 'animation']
    }
  };

  const playgroundTabs = [
    {
      id: 'molecular',
      label: '3D Molecular',
      icon: <Atom className="h-4 w-4" />,
      description: 'Interactive 3D molecular visualizations and chemical reactions'
    },
    {
      id: 'code',
      label: 'Code Editor',
      icon: <Code className="h-4 w-4" />,
      description: 'Real-time code editing with live preview'
    },
    {
      id: 'controls',
      label: 'Advanced Controls',
      icon: <Settings className="h-4 w-4" />,
      description: 'Fine-tune parameters and visual effects'
    },
    {
      id: 'export',
      label: 'Export & Share',
      icon: <Download className="h-4 w-4" />,
      description: 'Export your creations and share with others'
    }
  ];

  return (
    <Card className="w-full max-w-7xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-yellow-400" />
            Interactive Playground
            <Badge variant="secondary" className="ml-2">Advanced</Badge>
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-400">
            <Layers className="h-4 w-4" />
            Multi-Feature Environment
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Feature Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {playgroundTabs.map((tab) => (
              <Card 
                key={tab.id}
                className={`cursor-pointer transition-all duration-200 ${
                  activeTab === tab.id 
                    ? 'bg-blue-500/20 border-blue-500/50' 
                    : 'bg-slate-700/50 border-slate-600 hover:bg-slate-700/70'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {tab.icon}
                    <span className="text-white font-medium">{tab.label}</span>
                  </div>
                  <p className="text-xs text-slate-400">{tab.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Content Area */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-700">
              {playgroundTabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                  {tab.icon}
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="molecular" className="mt-6">
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Atom className="h-5 w-5" />
                    3D Molecular Visualization
                  </h3>
                  <p className="text-slate-300 text-sm">
                    Explore interactive 3D molecular structures and chemical reactions with real-time controls.
                  </p>
                </div>
                <MolecularPlayground />
              </div>
            </TabsContent>

            <TabsContent value="code" className="mt-6">
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-4 border border-green-500/20">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Real-time Code Editor
                  </h3>
                  <p className="text-slate-300 text-sm">
                    Write HTML, CSS, and JavaScript with live preview and syntax highlighting.
                  </p>
                </div>
                <CodeEditor
                  onCodeChange={handleCodeChange}
                  showPreview={true}
                  autoRun={true}
                />
              </div>
            </TabsContent>

            <TabsContent value="controls" className="mt-6">
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg p-4 border border-purple-500/20">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Advanced Parameter Controls
                  </h3>
                  <p className="text-slate-300 text-sm">
                    Fine-tune animations, visual effects, and interactive parameters in real-time.
                  </p>
                </div>
                <AdvancedControls
                  onParameterChange={handleParameterChange}
                  onPresetLoad={handlePresetLoad}
                  onExport={() => handleExport('config', parameters)}
                  onReset={() => setParameters({})}
                />
              </div>
            </TabsContent>

            <TabsContent value="export" className="mt-6">
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg p-4 border border-orange-500/20">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Export & Share System
                  </h3>
                  <p className="text-slate-300 text-sm">
                    Export your creations in multiple formats and share them with the world.
                  </p>
                </div>
                <ExportSystem
                  data={exportData}
                  onExport={handleExport}
                  onShare={handleShare}
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Status Bar */}
          <div className="bg-slate-700/50 rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-slate-300">System Active</span>
                </div>
                <div className="text-slate-400">
                  Active Tab: <span className="text-white">{playgroundTabs.find(t => t.id === activeTab)?.label}</span>
                </div>
              </div>
              <div className="flex items-center gap-4 text-slate-400">
                <span>Parameters: {Object.keys(parameters).length}</span>
                <span>Code Lines: {Object.values(codeContent).reduce((acc, code) => acc + code.split('\n').length, 0)}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
