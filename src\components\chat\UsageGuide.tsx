import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BookOpen, 
  MessageSquare, 
  Zap, 
  Settings, 
  HelpCircle,
  CheckCircle,
  ArrowRight,
  Lightbulb,
  Target,
  Rocket
} from 'lucide-react';

export const UsageGuide: React.FC = () => {
  const [activeTab, setActiveTab] = useState('getting-started');

  const quickStartSteps = [
    {
      step: 1,
      title: "Start Chatting",
      description: "Type any message in the chat input below",
      icon: <MessageSquare className="h-5 w-5" />,
      example: "Hello! Can you help me with CSS animations?"
    },
    {
      step: 2,
      title: "Get Enhanced Responses",
      description: "Receive intelligent responses with interactive features",
      icon: <Zap className="h-5 w-5" />,
      example: "Responses include live code examples and demos"
    },
    {
      step: 3,
      title: "Explore Features",
      description: "Use the demo section to explore all capabilities",
      icon: <Settings className="h-5 w-5" />,
      example: "Click 'See Features Demo' to explore interactive tools"
    }
  ];

  const samplePrompts = [
    {
      category: "Web Development",
      prompts: [
        "Create a CSS animation for a bouncing ball",
        "Show me how to build a responsive navigation menu",
        "Generate an interactive color picker with JavaScript",
        "Create a flexbox layout example with live controls"
      ]
    },
    {
      category: "3D & Molecular",
      prompts: [
        "Show me a 3D animation of water molecules",
        "Create an interactive chemical reaction animation",
        "Demonstrate molecular geometry with benzene",
        "Animate the formation of carbonic acid"
      ]
    },
    {
      category: "Interactive Demos",
      prompts: [
        "Build an interactive parameter control demo",
        "Create a live code editor example",
        "Show me real-time animation controls",
        "Generate an export system demonstration"
      ]
    }
  ];

  const aiServiceModes = [
    {
      mode: "Live AI Mode",
      status: "🤖 Connected",
      description: "Real AI responses from Puter AI service",
      features: ["Real-time AI responses", "Advanced reasoning", "Context awareness", "Enhanced with interactive features"],
      color: "border-green-500/30 bg-green-500/10"
    },
    {
      mode: "Demo Mode",
      status: "🎭 Active",
      description: "Intelligent demo responses with full functionality",
      features: ["Smart pattern matching", "Interactive examples", "Educational content", "All features available"],
      color: "border-blue-500/30 bg-blue-500/10"
    }
  ];

  return (
    <Card className="w-full max-w-6xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <BookOpen className="h-6 w-6 text-blue-400" />
          How to Use Chat Wizard Scribe Hub
          <Badge variant="secondary" className="ml-2">Complete Guide</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700">
            <TabsTrigger value="getting-started" className="flex items-center gap-2">
              <Rocket className="h-4 w-4" />
              Quick Start
            </TabsTrigger>
            <TabsTrigger value="ai-modes" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              AI Modes
            </TabsTrigger>
            <TabsTrigger value="examples" className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Examples
            </TabsTrigger>
            <TabsTrigger value="features" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Features
            </TabsTrigger>
          </TabsList>

          <TabsContent value="getting-started" className="mt-6">
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-6 border border-blue-500/20">
                <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                  <Rocket className="h-5 w-5 text-blue-400" />
                  Quick Start Guide
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {quickStartSteps.map((step) => (
                    <div key={step.step} className="bg-slate-700/50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {step.step}
                        </div>
                        {step.icon}
                        <h4 className="text-white font-medium">{step.title}</h4>
                      </div>
                      <p className="text-slate-300 text-sm mb-3">{step.description}</p>
                      <div className="bg-slate-800/50 rounded p-2">
                        <code className="text-xs text-blue-300">{step.example}</code>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-slate-700/50 rounded-lg p-6">
                <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  What You Can Do Right Now
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="text-green-400 font-medium">✅ Available Features:</h4>
                    <ul className="text-sm text-slate-300 space-y-2">
                      <li className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-blue-400" />
                        Chat with intelligent AI responses
                      </li>
                      <li className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-blue-400" />
                        Get live code examples and demos
                      </li>
                      <li className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-blue-400" />
                        Explore 3D molecular animations
                      </li>
                      <li className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-blue-400" />
                        Use interactive parameter controls
                      </li>
                      <li className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-blue-400" />
                        Export and share your creations
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="text-blue-400 font-medium">🎯 Try These Commands:</h4>
                    <div className="space-y-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full justify-start text-left bg-slate-800/50 border-slate-600 text-slate-300 hover:bg-slate-700/50"
                        onClick={() => {
                          const input = document.querySelector('textarea') as HTMLTextAreaElement;
                          if (input) {
                            input.value = "Create a CSS animation demo";
                            input.focus();
                          }
                        }}
                      >
                        "Create a CSS animation demo"
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full justify-start text-left bg-slate-800/50 border-slate-600 text-slate-300 hover:bg-slate-700/50"
                        onClick={() => {
                          const input = document.querySelector('textarea') as HTMLTextAreaElement;
                          if (input) {
                            input.value = "Show me 3D molecular animations";
                            input.focus();
                          }
                        }}
                      >
                        "Show me 3D molecular animations"
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full justify-start text-left bg-slate-800/50 border-slate-600 text-slate-300 hover:bg-slate-700/50"
                        onClick={() => {
                          const input = document.querySelector('textarea') as HTMLTextAreaElement;
                          if (input) {
                            input.value = "Build an interactive HTML example";
                            input.focus();
                          }
                        }}
                      >
                        "Build an interactive HTML example"
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ai-modes" className="mt-6">
            <div className="space-y-6">
              <div className="bg-slate-700/50 rounded-lg p-6">
                <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-400" />
                  AI Service Modes
                </h3>
                <p className="text-slate-300 mb-6">
                  Chat Wizard Scribe Hub automatically detects available AI services and provides the best possible experience:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {aiServiceModes.map((mode) => (
                    <div key={mode.mode} className={`rounded-lg p-4 border ${mode.color}`}>
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-white font-medium">{mode.mode}</h4>
                        <Badge variant="outline" className="text-xs">{mode.status}</Badge>
                      </div>
                      <p className="text-slate-300 text-sm mb-4">{mode.description}</p>
                      <div className="space-y-2">
                        <h5 className="text-sm font-medium text-slate-200">Features:</h5>
                        <ul className="text-xs text-slate-400 space-y-1">
                          {mode.features.map((feature, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-green-400" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="examples" className="mt-6">
            <div className="space-y-6">
              {samplePrompts.map((category) => (
                <div key={category.category} className="bg-slate-700/50 rounded-lg p-6">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-400" />
                    {category.category} Examples
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {category.prompts.map((prompt, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        className="justify-start text-left h-auto p-3 bg-slate-800/50 border-slate-600 text-slate-300 hover:bg-slate-700/50"
                        onClick={() => {
                          const input = document.querySelector('textarea') as HTMLTextAreaElement;
                          if (input) {
                            input.value = prompt;
                            input.focus();
                          }
                        }}
                      >
                        <div className="flex items-start gap-2">
                          <ArrowRight className="h-4 w-4 mt-0.5 text-blue-400 flex-shrink-0" />
                          <span className="text-sm">{prompt}</span>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="features" className="mt-6">
            <div className="bg-slate-700/50 rounded-lg p-6">
              <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-400" />
                Complete Feature Overview
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h4 className="text-blue-400 font-medium">💬 Chat Features</h4>
                  <ul className="text-sm text-slate-300 space-y-2">
                    <li>• Enhanced markdown rendering</li>
                    <li>• Syntax-highlighted code blocks</li>
                    <li>• Live HTML/CSS/JS previews</li>
                    <li>• Interactive demonstrations</li>
                    <li>• Smart response enhancement</li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="text-green-400 font-medium">🧪 3D & Molecular</h4>
                  <ul className="text-sm text-slate-300 space-y-2">
                    <li>• 6+ molecule library</li>
                    <li>• Chemical reaction animations</li>
                    <li>• Interactive 3D controls</li>
                    <li>• Scientific accuracy</li>
                    <li>• Educational visualizations</li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="text-purple-400 font-medium">⚡ Advanced Tools</h4>
                  <ul className="text-sm text-slate-300 space-y-2">
                    <li>• Real-time code editor</li>
                    <li>• Parameter control system</li>
                    <li>• Multi-format export</li>
                    <li>• Performance monitoring</li>
                    <li>• Quality assurance tools</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
