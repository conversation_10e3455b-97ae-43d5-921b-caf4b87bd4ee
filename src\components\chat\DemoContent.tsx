import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageRenderer } from './MessageRenderer';
import { TestMessage } from './TestMessage';
import { LivePreviewTest } from './LivePreviewTest';
import { MolecularPlayground } from './MolecularPlayground';
import { InteractivePlayground } from './InteractivePlayground';
import { QualityAssurance } from './QualityAssurance';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Sparkles, Code, Palette, BarChart3, TestTube, Zap, Atom, Settings, Shield } from 'lucide-react';

export const DemoContent: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState('basic');

  const demoContents = {
    basic: `# 🎨 Enhanced Chat Response Features

Welcome to the **enhanced chat experience**! This system now supports rich formatting, interactive code examples, and visual demonstrations.

## Key Features

- **Rich Markdown Support** with syntax highlighting
- **Live Code Previews** for HTML, CSS, and JavaScript
- **Interactive Demonstrations** with real-time parameter adjustment
- **Visual Diagrams** and charts
- **Mathematical Expressions** with LaTeX support

> 💡 **Tip**: Try asking for code examples, interactive demos, or visual explanations to see these features in action!

### Example Code Block

\`\`\`javascript
function createGradient(colors) {
  return \`linear-gradient(135deg, \${colors.join(', ')})\`;
}

const beautifulGradient = createGradient([
  '#667eea',
  '#764ba2'
]);

console.log(beautifulGradient);
\`\`\`

---

**Ready to explore?** Select different demo types from the tabs above!`,

    livePreview: `# 🚀 Live Code Preview Demo

Here's an interactive HTML example that you can modify and see the results in real-time:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Card</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            color: white;
            max-width: 400px;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-10px);
        }

        .card h1 {
            margin: 0 0 20px 0;
            font-size: 2.5em;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            color: white;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>✨ Magic Card</h1>
        <p>This is a beautiful glassmorphism card with interactive effects!</p>
        <button class="button" onclick="changeBackground()">Change Magic</button>
    </div>

    <script>
        const gradients = [
            'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
        ];

        function changeBackground() {
            const randomGradient = gradients[Math.floor(Math.random() * gradients.length)];
            document.body.style.background = randomGradient;

            // Add a little animation
            document.querySelector('.card').style.transform = 'scale(1.05)';
            setTimeout(() => {
                document.querySelector('.card').style.transform = 'scale(1)';
            }, 200);
        }
    </script>
</body>
</html>
\`\`\`

**Try it out!** You can edit the code above and see the changes in real-time in the preview panel.`,

    interactive: `# 🎮 Interactive Demonstrations

This content includes interactive elements that you can manipulate:

## CSS Animation Demo
<!-- INTERACTIVE:CSS_ANIMATION -->

Adjust the animation parameters above to see how different values affect the animation behavior.

## Color Picker Demo
<!-- INTERACTIVE:COLOR_PICKER -->

Experiment with HSL color values to understand how hue, saturation, and lightness work together.

## Layout Demo
<!-- INTERACTIVE:LAYOUT_DEMO -->

Play with flexbox properties to see how they affect element positioning.

---

These interactive demos help you understand concepts through hands-on experimentation!`,

    diagrams: `# 📊 Visual Diagrams and Charts

Visual representations make complex concepts easier to understand:

## Process Flow Diagram
<!-- DIAGRAM -->

## Sample Chart Data
\`\`\`mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process A]
    B -->|No| D[Process B]
    C --> E[End]
    D --> E
\`\`\`

## Mathematical Expressions

You can also include mathematical formulas:

$$E = mc^2$$

And inline math like $\\pi r^2$ for the area of a circle.

---

Visual elements help communicate ideas more effectively than text alone!`
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <Sparkles className="h-6 w-6 text-yellow-400" />
          Enhanced Response Features Demo
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedDemo} onValueChange={setSelectedDemo} className="w-full">
          <TabsList className="grid w-full grid-cols-9 bg-slate-700">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Basic
            </TabsTrigger>
            <TabsTrigger value="livePreview" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Live Code
            </TabsTrigger>
            <TabsTrigger value="interactive" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Interactive
            </TabsTrigger>
            <TabsTrigger value="diagrams" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Diagrams
            </TabsTrigger>
            <TabsTrigger value="molecular" className="flex items-center gap-2">
              <Atom className="h-4 w-4" />
              3D Molecular
            </TabsTrigger>
            <TabsTrigger value="playground" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Playground
            </TabsTrigger>
            <TabsTrigger value="qa" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              QA
            </TabsTrigger>
            <TabsTrigger value="test" className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              Test
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>

          {Object.entries(demoContents).map(([key, content]) => (
            <TabsContent key={key} value={key} className="mt-6">
              <div className="bg-slate-900/50 rounded-lg p-6">
                <MessageRenderer content={content} />
              </div>
            </TabsContent>
          ))}

          <TabsContent value="test" className="mt-6">
            <TestMessage />
          </TabsContent>

          <TabsContent value="molecular" className="mt-6">
            <MolecularPlayground />
          </TabsContent>

          <TabsContent value="playground" className="mt-6">
            <InteractivePlayground />
          </TabsContent>

          <TabsContent value="qa" className="mt-6">
            <QualityAssurance />
          </TabsContent>

          <TabsContent value="preview" className="mt-6">
            <LivePreviewTest />
          </TabsContent>
        </Tabs>

        <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <h4 className="text-blue-300 font-semibold mb-2">🎯 How to Use These Features</h4>
          <ul className="text-blue-200 text-sm space-y-1">
            <li>• Ask for code examples to see syntax highlighting and live previews</li>
            <li>• Request interactive demos by mentioning "interactive" or "demo"</li>
            <li>• Ask for diagrams or visual explanations to see charts and flowcharts</li>
            <li>• Include mathematical expressions for LaTeX rendering</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
