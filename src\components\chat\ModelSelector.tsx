
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Bot } from 'lucide-react';

const models = [
  { id: 'gpt-4o-mini', name: 'GPT-4o Mini', provider: 'OpenAI', description: 'Fast and efficient for most tasks' },
  { id: 'gpt-4o', name: 'GPT-4o', provider: 'OpenAI', description: 'Most capable model for complex tasks' },
  { id: 'o1', name: 'o1', provider: 'OpenAI', description: 'Advanced reasoning model' },
  { id: 'o1-mini', name: 'o1 Mini', provider: 'OpenAI', description: 'Faster reasoning model' },
  { id: 'o1-pro', name: 'o1 Pro', provider: 'OpenAI', description: 'Professional reasoning model' },
  { id: 'o3', name: 'o3', provider: 'OpenAI', description: 'Latest reasoning model' },
  { id: 'o3-mini', name: 'o3 Mini', provider: 'OpenAI', description: 'Compact reasoning model' },
  { id: 'o4-mini', name: 'o4 Mini', provider: 'OpenAI', description: 'Next-gen compact model' },
  { id: 'gpt-4.1', name: 'GPT-4.1', provider: 'OpenAI', description: 'Enhanced GPT-4' },
  { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', provider: 'OpenAI', description: 'Compact GPT-4.1' },
  { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', provider: 'OpenAI', description: 'Ultra-compact GPT-4.1' },
  { id: 'gpt-4.5-preview', name: 'GPT-4.5 Preview', provider: 'OpenAI', description: 'Preview of GPT-4.5' },
  { id: 'claude-sonnet-4', name: 'Claude Sonnet 4', provider: 'Anthropic', description: 'Latest Claude model' },
  { id: 'claude-opus-4', name: 'Claude Opus 4', provider: 'Anthropic', description: 'Most capable Claude' },
  { id: 'claude-3-7-sonnet', name: 'Claude 3.7 Sonnet', provider: 'Anthropic', description: 'Enhanced Claude 3.5' },
  { id: 'claude-3-5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic', description: 'Excellent for creative tasks' },
  { id: 'deepseek-chat', name: 'DeepSeek Chat', provider: 'DeepSeek', description: 'Advanced AI reasoning' },
  { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner', provider: 'DeepSeek', description: 'Specialized reasoning model' },
  { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', provider: 'Google', description: 'Fast multimodal model' },
  { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', provider: 'Google', description: 'Versatile AI model' },
  { id: 'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo', name: 'Llama 3.1 8B Turbo', provider: 'Meta', description: 'Fast Llama model' },
  { id: 'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo', name: 'Llama 3.1 70B Turbo', provider: 'Meta', description: 'Powerful Llama model' },
  { id: 'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo', name: 'Llama 3.1 405B Turbo', provider: 'Meta', description: 'Most capable Llama' },
  { id: 'mistral-large-latest', name: 'Mistral Large', provider: 'Mistral AI', description: 'Large Mistral model' },
  { id: 'pixtral-large-latest', name: 'Pixtral Large', provider: 'Mistral AI', description: 'Multimodal Mistral' },
  { id: 'codestral-latest', name: 'Codestral', provider: 'Mistral AI', description: 'Code-specialized Mistral' },
  { id: 'google/gemma-2-27b-it', name: 'Gemma 2 27B', provider: 'Google', description: 'Open Gemma model' },
  { id: 'grok-beta', name: 'Grok Beta', provider: 'xAI', description: 'xAI\'s conversational model' },
];

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({ selectedModel, onModelChange }) => {
  const selectedModelInfo = models.find(m => m.id === selectedModel);

  return (
    <Select value={selectedModel} onValueChange={onModelChange}>
      <SelectTrigger className="w-56 bg-white/10 border-white/20 text-white hover:bg-white/20">
        <div className="flex items-center gap-2">
          <Bot className="h-4 w-4" />
          <div className="flex flex-col items-start">
            <span className="text-sm font-medium">{selectedModelInfo?.name || 'Select Model'}</span>
            {selectedModelInfo && (
              <span className="text-xs text-white/60">{selectedModelInfo.provider}</span>
            )}
          </div>
        </div>
      </SelectTrigger>
      <SelectContent className="bg-slate-800 border-slate-700 max-h-96">
        {models.map((model) => (
          <SelectItem 
            key={model.id} 
            value={model.id}
            className="text-white hover:bg-slate-700 focus:bg-slate-700"
          >
            <div className="flex flex-col items-start">
              <div className="flex items-center gap-2">
                <span className="font-medium">{model.name}</span>
                <span className="text-xs text-slate-400">{model.provider}</span>
              </div>
              <span className="text-xs text-slate-400">{model.description}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
