import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Wifi, 
  WifiOff, 
  Bot, 
  TestTube, 
  AlertCircle, 
  CheckCircle,
  Zap
} from 'lucide-react';

interface AIServiceStatus {
  isConnected: boolean;
  service: 'puter' | 'demo' | 'offline';
  lastChecked: Date;
  responseTime?: number;
}

export const AIStatusIndicator: React.FC = () => {
  const [status, setStatus] = useState<AIServiceStatus>({
    isConnected: false,
    service: 'demo',
    lastChecked: new Date()
  });

  const checkAIStatus = async () => {
    const startTime = Date.now();
    
    try {
      // Check if Puter AI is available
      if ((window as any).puter?.ai?.chat) {
        const responseTime = Date.now() - startTime;
        setStatus({
          isConnected: true,
          service: 'puter',
          lastChecked: new Date(),
          responseTime
        });
      } else {
        setStatus({
          isConnected: false,
          service: 'demo',
          lastChecked: new Date()
        });
      }
    } catch (error) {
      setStatus({
        isConnected: false,
        service: 'offline',
        lastChecked: new Date()
      });
    }
  };

  useEffect(() => {
    checkAIStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkAIStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = () => {
    switch (status.service) {
      case 'puter':
        return <Bot className="h-4 w-4 text-green-400" />;
      case 'demo':
        return <TestTube className="h-4 w-4 text-blue-400" />;
      case 'offline':
        return <WifiOff className="h-4 w-4 text-red-400" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-400" />;
    }
  };

  const getStatusText = () => {
    switch (status.service) {
      case 'puter':
        return 'Live AI Connected';
      case 'demo':
        return 'Demo Mode Active';
      case 'offline':
        return 'AI Offline';
      default:
        return 'Unknown Status';
    }
  };

  const getStatusColor = () => {
    switch (status.service) {
      case 'puter':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'demo':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'offline':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      default:
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
    }
  };

  const getTooltipContent = () => {
    const lastCheckedTime = status.lastChecked.toLocaleTimeString();
    
    switch (status.service) {
      case 'puter':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="font-semibold">Puter AI Connected</span>
            </div>
            <div className="text-sm text-slate-300">
              • Real AI responses available
              {status.responseTime && <div>• Response time: {status.responseTime}ms</div>}
              <div>• Last checked: {lastCheckedTime}</div>
            </div>
          </div>
        );
      case 'demo':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <TestTube className="h-4 w-4 text-blue-400" />
              <span className="font-semibold">Demo Mode Active</span>
            </div>
            <div className="text-sm text-slate-300">
              • Intelligent demo responses
              • All features fully functional
              • Enhanced examples available
              <div>• Last checked: {lastCheckedTime}</div>
            </div>
          </div>
        );
      case 'offline':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <WifiOff className="h-4 w-4 text-red-400" />
              <span className="font-semibold">AI Service Offline</span>
            </div>
            <div className="text-sm text-slate-300">
              • No AI service available
              • Check your connection
              • Try refreshing the page
              <div>• Last checked: {lastCheckedTime}</div>
            </div>
          </div>
        );
      default:
        return 'Checking AI service status...';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={`${getStatusColor()} cursor-help transition-all duration-200 hover:scale-105`}
          >
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="text-xs font-medium">{getStatusText()}</span>
              {status.service === 'puter' && (
                <Zap className="h-3 w-3 text-green-400 animate-pulse" />
              )}
            </div>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="bg-slate-800 border-slate-600 max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
