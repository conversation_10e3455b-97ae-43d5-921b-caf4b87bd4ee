import React, { useState, useEffect, useRef } from 'react';
import { CodeBlock } from './CodeBlock';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, RefreshCw, Maximize2, Code2, Eye } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface LivePreviewProps {
  code: string;
  language: string;
  showPreview?: boolean;
  title?: string;
}

export const LivePreview: React.FC<LivePreviewProps> = ({
  code,
  language,
  showPreview = true,
  title,
}) => {
  const [htmlCode, setHtmlCode] = useState('');
  const [cssCode, setCssCode] = useState('');
  const [jsCode, setJsCode] = useState('');
  const [activeTab, setActiveTab] = useState('preview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const fullscreenIframeRef = useRef<HTMLIFrameElement>(null);

  // Parse the code based on language
  useEffect(() => {
    if (language === 'html') {
      // Extract CSS and JS from HTML if present
      const cssMatch = code.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      const jsMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/i);

      let cleanHtml = code;
      if (cssMatch) {
        setCssCode(cssMatch[1].trim());
        cleanHtml = cleanHtml.replace(cssMatch[0], '');
      }
      if (jsMatch) {
        setJsCode(jsMatch[1].trim());
        cleanHtml = cleanHtml.replace(jsMatch[0], '');
      }

      setHtmlCode(cleanHtml.trim());
    } else if (language === 'css') {
      setCssCode(code);
      setHtmlCode('<div class="demo">CSS Demo</div>');
    } else if (language === 'javascript' || language === 'js') {
      setJsCode(code);
      setHtmlCode('<div id="demo">JavaScript Demo</div>');
    }
  }, [code, language]);

  const generatePreviewContent = () => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .demo {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }
        ${cssCode}
    </style>
</head>
<body>
    ${htmlCode}
    <script>
        try {
            ${jsCode}
        } catch (error) {
            console.error('JavaScript Error:', error);
            document.body.innerHTML += '<div style="background: #fee; color: #c33; padding: 10px; border-radius: 4px; margin: 10px 0;">JavaScript Error: ' + error.message + '</div>';
        }
    </script>
</body>
</html>`;
  };

  const updatePreview = () => {
    // Force re-render of iframes by updating srcDoc
    if (iframeRef.current) {
      iframeRef.current.srcDoc = generatePreviewContent();
    }
    if (fullscreenIframeRef.current) {
      fullscreenIframeRef.current.srcDoc = generatePreviewContent();
    }
  };

  const PreviewContent = () => (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-slate-800">
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="html" className="flex items-center gap-2">
            <Code2 className="h-4 w-4" />
            HTML
          </TabsTrigger>
          <TabsTrigger value="css" className="flex items-center gap-2">
            <Code2 className="h-4 w-4" />
            CSS
          </TabsTrigger>
          <TabsTrigger value="js" className="flex items-center gap-2">
            <Code2 className="h-4 w-4" />
            JS
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="mt-4">
          <div className="relative">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-slate-300">Live Preview</h4>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={updatePreview}
                  className="h-8 text-slate-400 hover:text-white"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-slate-400 hover:text-white"
                    >
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>Live Preview - Fullscreen</DialogTitle>
                    </DialogHeader>
                    <iframe
                      ref={fullscreenIframeRef}
                      className="w-full h-full border border-slate-600 rounded-lg"
                      title="Live Preview Fullscreen"
                      srcDoc={generatePreviewContent()}
                    />
                  </DialogContent>
                </Dialog>
              </div>
            </div>
            <iframe
              ref={iframeRef}
              className="w-full h-64 border border-slate-600 rounded-lg bg-white"
              title="Live Preview"
              srcDoc={generatePreviewContent()}
            />
          </div>
        </TabsContent>

        <TabsContent value="html" className="mt-4">
          <CodeBlock
            code={htmlCode}
            language="html"
            editable={true}
            onCodeChange={setHtmlCode}
            title="HTML"
          />
        </TabsContent>

        <TabsContent value="css" className="mt-4">
          <CodeBlock
            code={cssCode}
            language="css"
            editable={true}
            onCodeChange={setCssCode}
            title="CSS"
          />
        </TabsContent>

        <TabsContent value="js" className="mt-4">
          <CodeBlock
            code={jsCode}
            language="javascript"
            editable={true}
            onCodeChange={setJsCode}
            title="JavaScript"
          />
        </TabsContent>
      </Tabs>
    </div>
  );

  if (!showPreview) {
    return <CodeBlock code={code} language={language} title={title} />;
  }

  return (
    <div className="my-6">
      <div className="bg-slate-800/80 backdrop-blur-sm border border-slate-600 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <Play className="h-5 w-5 text-green-400" />
            {title || 'Interactive Code Demo'}
          </h3>
        </div>
        <PreviewContent />
      </div>
    </div>
  );
};
