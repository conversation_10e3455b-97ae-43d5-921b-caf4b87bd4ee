
declare global {
  interface Window {
    puter: {
      ai: {
        chat: (
          prompt: string | Array<{ role: string; content: string }>,
          options?: {
            model?: string;
            stream?: boolean;
            tools?: Array<any>;
          }
        ) => Promise<any>;
        txt2img: (prompt: string, testMode?: boolean) => Promise<HTMLImageElement>;
        img2txt: (image: string | File | Blob, testMode?: boolean) => Promise<string>;
        txt2speech: (text: string, language?: string, testMode?: boolean) => Promise<HTMLAudioElement>;
      };
      auth: {
        isSignedIn: () => boolean;
        signIn: () => Promise<boolean>;
        getUser: () => Promise<{ uuid: string; username: string; email_confirmed: boolean }>;
      };
      kv: {
        get: (key: string) => Promise<string | null>;
        set: (key: string, value: string) => Promise<boolean>;
        list: (pattern?: string, returnValues?: boolean) => Promise<string[] | Array<{ key: string; value: string }>>;
        del: (key: string) => Promise<boolean>;
      };
      print: (text: string) => void;
      randName: (separator?: string) => string;
    };
  }
}

export {};
