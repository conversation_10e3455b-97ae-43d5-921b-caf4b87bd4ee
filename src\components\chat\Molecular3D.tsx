import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Play, Pause, RotateCcw, Download, Settings } from 'lucide-react';

interface Atom {
  id: string;
  element: string;
  x: number;
  y: number;
  z: number;
  color: string;
  radius: number;
}

interface Bond {
  id: string;
  atom1: string;
  atom2: string;
  type: 'single' | 'double' | 'triple';
  strength: number;
}

interface Molecule {
  id: string;
  name: string;
  formula: string;
  atoms: Atom[];
  bonds: Bond[];
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
}

interface Molecular3DProps {
  molecules?: Molecule[];
  reactionType?: 'formation' | 'decomposition' | 'substitution';
  animationSpeed?: number;
  showControls?: boolean;
}

export const Molecular3D: React.FC<Molecular3DProps> = ({
  molecules = [],
  reactionType = 'formation',
  animationSpeed = 1,
  showControls = true
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [speed, setSpeed] = useState(animationSpeed);
  const [rotationX, setRotationX] = useState(0);
  const [rotationY, setRotationY] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [showBonds, setShowBonds] = useState(true);
  const [showLabels, setShowLabels] = useState(true);

  // Default molecules for water formation reaction
  const defaultMolecules: Molecule[] = [
    {
      id: 'h2o',
      name: 'Water',
      formula: 'H₂O',
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      atoms: [
        { id: 'o1', element: 'O', x: 0, y: 0, z: 0, color: '#ff4444', radius: 20 },
        { id: 'h1', element: 'H', x: -25, y: -15, z: 5, color: '#ffffff', radius: 12 },
        { id: 'h2', element: 'H', x: 25, y: -15, z: -5, color: '#ffffff', radius: 12 }
      ],
      bonds: [
        { id: 'b1', atom1: 'o1', atom2: 'h1', type: 'single', strength: 1 },
        { id: 'b2', atom1: 'o1', atom2: 'h2', type: 'single', strength: 1 }
      ]
    },
    {
      id: 'co2',
      name: 'Carbon Dioxide',
      formula: 'CO₂',
      position: { x: 150, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      atoms: [
        { id: 'c1', element: 'C', x: 0, y: 0, z: 0, color: '#444444', radius: 16 },
        { id: 'o2', element: 'O', x: -35, y: 0, z: 0, color: '#ff4444', radius: 20 },
        { id: 'o3', element: 'O', x: 35, y: 0, z: 0, color: '#ff4444', radius: 20 }
      ],
      bonds: [
        { id: 'b3', atom1: 'c1', atom2: 'o2', type: 'double', strength: 2 },
        { id: 'b4', atom1: 'c1', atom2: 'o3', type: 'double', strength: 2 }
      ]
    }
  ];

  const activeMolecules = molecules.length > 0 ? molecules : defaultMolecules;

  // Animation frame management
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentFrame(prev => (prev + speed) % 360);
      setRotationY(prev => (prev + speed * 0.5) % 360);
      setRotationX(prev => (prev + speed * 0.3) % 360);
    }, 50);

    return () => clearInterval(interval);
  }, [isPlaying, speed]);

  // 3D to 2D projection
  const project3D = (x: number, y: number, z: number, molPos: { x: number; y: number; z: number }) => {
    const centerX = 200;
    const centerY = 150;
    
    // Apply molecular position
    const worldX = x + molPos.x;
    const worldY = y + molPos.y;
    const worldZ = z + molPos.z;
    
    // Apply rotation
    const radX = (rotationX * Math.PI) / 180;
    const radY = (rotationY * Math.PI) / 180;
    
    // Rotate around Y axis
    const rotatedX = worldX * Math.cos(radY) - worldZ * Math.sin(radY);
    const rotatedZ = worldX * Math.sin(radY) + worldZ * Math.cos(radY);
    
    // Rotate around X axis
    const finalY = worldY * Math.cos(radX) - rotatedZ * Math.sin(radX);
    const finalZ = worldY * Math.sin(radX) + rotatedZ * Math.cos(radX);
    
    // Apply perspective and zoom
    const perspective = 500;
    const scale = (perspective / (perspective + finalZ)) * zoom;
    
    return {
      x: centerX + rotatedX * scale,
      y: centerY + finalY * scale,
      scale: scale
    };
  };

  // Render atom
  const renderAtom = (atom: Atom, molPos: { x: number; y: number; z: number }) => {
    const projected = project3D(atom.x, atom.y, atom.z, molPos);
    const radius = atom.radius * projected.scale;
    
    return (
      <g key={atom.id}>
        <defs>
          <radialGradient id={`gradient-${atom.id}`} cx="30%" cy="30%">
            <stop offset="0%" stopColor={atom.color} stopOpacity="1" />
            <stop offset="70%" stopColor={atom.color} stopOpacity="0.8" />
            <stop offset="100%" stopColor="#000000" stopOpacity="0.3" />
          </radialGradient>
          <filter id={`shadow-${atom.id}`}>
            <feDropShadow dx="2" dy="2" stdDeviation="3" floodColor="#00000040"/>
          </filter>
        </defs>
        <circle
          cx={projected.x}
          cy={projected.y}
          r={radius}
          fill={`url(#gradient-${atom.id})`}
          filter={`url(#shadow-${atom.id})`}
          stroke="#ffffff"
          strokeWidth={0.5}
        />
        {showLabels && (
          <text
            x={projected.x}
            y={projected.y + 4}
            textAnchor="middle"
            fontSize={Math.max(8, 12 * projected.scale)}
            fill="#ffffff"
            fontWeight="bold"
            style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
          >
            {atom.element}
          </text>
        )}
      </g>
    );
  };

  // Render bond
  const renderBond = (bond: Bond, molecule: Molecule) => {
    if (!showBonds) return null;
    
    const atom1 = molecule.atoms.find(a => a.id === bond.atom1);
    const atom2 = molecule.atoms.find(a => a.id === bond.atom2);
    
    if (!atom1 || !atom2) return null;
    
    const proj1 = project3D(atom1.x, atom1.y, atom1.z, molecule.position);
    const proj2 = project3D(atom2.x, atom2.y, atom2.z, molecule.position);
    
    const bondWidth = bond.type === 'single' ? 2 : bond.type === 'double' ? 4 : 6;
    
    return (
      <line
        key={bond.id}
        x1={proj1.x}
        y1={proj1.y}
        x2={proj2.x}
        y2={proj2.y}
        stroke="#ffffff"
        strokeWidth={bondWidth * Math.min(proj1.scale, proj2.scale)}
        opacity={0.8}
        style={{ filter: 'drop-shadow(1px 1px 2px rgba(0,0,0,0.5))' }}
      />
    );
  };

  const togglePlayPause = () => setIsPlaying(!isPlaying);
  const resetAnimation = () => {
    setCurrentFrame(0);
    setRotationX(0);
    setRotationY(0);
  };

  const exportSVG = () => {
    if (svgRef.current) {
      const svgData = new XMLSerializer().serializeToString(svgRef.current);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `molecular-animation-${Date.now()}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);
    }
  };

  return (
    <Card className="w-full bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <span>🧬 3D Molecular Animation</span>
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" onClick={togglePlayPause}>
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={resetAnimation}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={exportSVG}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 3D SVG Viewport */}
          <div className="bg-gradient-to-br from-slate-900 to-slate-800 rounded-lg p-4 border border-slate-600">
            <svg
              ref={svgRef}
              width="400"
              height="300"
              viewBox="0 0 400 300"
              className="w-full h-auto border border-slate-700 rounded"
              style={{ background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)' }}
            >
              {/* Render molecules */}
              {activeMolecules.map(molecule => (
                <g key={molecule.id}>
                  {/* Render bonds first (behind atoms) */}
                  {molecule.bonds.map(bond => renderBond(bond, molecule))}
                  {/* Render atoms */}
                  {molecule.atoms.map(atom => renderAtom(atom, molecule.position))}
                </g>
              ))}
              
              {/* Molecule labels */}
              {activeMolecules.map((molecule, index) => {
                const labelPos = project3D(0, -60, 0, molecule.position);
                return (
                  <text
                    key={`label-${molecule.id}`}
                    x={labelPos.x}
                    y={labelPos.y}
                    textAnchor="middle"
                    fontSize="14"
                    fill="#ffffff"
                    fontWeight="bold"
                  >
                    {molecule.formula}
                  </text>
                );
              })}
            </svg>
          </div>

          {/* Controls */}
          {showControls && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label className="text-white">Animation Speed</Label>
                  <Slider
                    value={[speed]}
                    onValueChange={(value) => setSpeed(value[0])}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                  <span className="text-sm text-slate-400">{speed.toFixed(1)}x</span>
                </div>
                
                <div>
                  <Label className="text-white">Zoom</Label>
                  <Slider
                    value={[zoom]}
                    onValueChange={(value) => setZoom(value[0])}
                    min={0.5}
                    max={2}
                    step={0.1}
                    className="mt-2"
                  />
                  <span className="text-sm text-slate-400">{zoom.toFixed(1)}x</span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2 text-white">
                    <input
                      type="checkbox"
                      checked={showBonds}
                      onChange={(e) => setShowBonds(e.target.checked)}
                      className="rounded"
                    />
                    Show Bonds
                  </label>
                  <label className="flex items-center gap-2 text-white">
                    <input
                      type="checkbox"
                      checked={showLabels}
                      onChange={(e) => setShowLabels(e.target.checked)}
                      className="rounded"
                    />
                    Show Labels
                  </label>
                </div>
                
                <div className="text-sm text-slate-300">
                  <p><strong>Frame:</strong> {currentFrame.toFixed(0)}°</p>
                  <p><strong>Rotation:</strong> X: {rotationX.toFixed(0)}° Y: {rotationY.toFixed(0)}°</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
