
import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageSquare, MoreHorizontal, Trash2 } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface Chat {
  id: string;
  title: string;
  updatedAt: string;
}

interface ChatHistoryProps {
  chats: Chat[];
  currentChatId: string | null;
  onSelectChat: (chatId: string) => void;
  onDeleteChat: (chatId: string) => void;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({ 
  chats, 
  currentChatId, 
  onSelectChat, 
  onDeleteChat 
}) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  if (chats.length === 0) {
    return (
      <div className="text-white/50 text-sm text-center py-4">
        No chats yet. Start a new conversation!
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {chats.map((chat) => (
        <div
          key={chat.id}
          className={`group relative rounded-lg transition-colors ${
            currentChatId === chat.id ? 'bg-white/20' : 'hover:bg-white/10'
          }`}
        >
          <Button
            variant="ghost"
            onClick={() => onSelectChat(chat.id)}
            className="w-full justify-start text-left h-auto p-3 text-white hover:bg-transparent group-hover:bg-transparent"
          >
            <div className="flex items-start gap-3 w-full">
              <MessageSquare className="h-4 w-4 text-white/60 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-white/90 truncate">
                  {chat.title}
                </div>
                <div className="text-xs text-white/50">
                  {formatTime(chat.updatedAt)}
                </div>
              </div>
            </div>
          </Button>
          
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-white/60 hover:text-white hover:bg-white/20"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-slate-800 border-slate-700">
                <DropdownMenuItem 
                  onClick={() => onDeleteChat(chat.id)}
                  className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      ))}
    </div>
  );
};
