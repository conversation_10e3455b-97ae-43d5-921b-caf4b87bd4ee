
import React from 'react';
import { <PERSON>rk<PERSON>, Zap, Shield, Globe } from 'lucide-react';

export const WelcomeScreen: React.FC = () => {
  const features = [
    {
      icon: Sparkles,
      title: 'Multiple AI Models',
      description: 'Choose from GPT-4, <PERSON>, and other cutting-edge AI models'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Get instant responses with our optimized infrastructure'
    },
    {
      icon: Shield,
      title: 'Privacy First',
      description: 'Your conversations are encrypted and secure'
    },
    {
      icon: Globe,
      title: 'Always Available',
      description: 'Access your AI assistant anywhere, anytime'
    }
  ];

  return (
    <div className="flex flex-col items-center justify-center h-full max-w-4xl mx-auto text-center">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-white mb-4">
          Welcome to <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Chat Wizard</span>
        </h1>
        <p className="text-xl text-white/70 max-w-2xl">
          Your intelligent AI companion for conversations, creativity, and problem-solving. 
          Start chatting to unlock the power of advanced AI models.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-3xl mb-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="bg-white/5 backdrop-blur-lg rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300"
          >
            <feature.icon className="h-8 w-8 text-blue-400 mb-3 mx-auto" />
            <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
            <p className="text-white/60 text-sm">{feature.description}</p>
          </div>
        ))}
      </div>
      
      <div className="text-white/40 text-sm">
        Type your message below to start a conversation
      </div>
    </div>
  );
};
