
import React, { useState } from 'react';
import { <PERSON>rkles, Zap, Shield, Globe, Code, Palette, BarChart3, Eye, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DemoContent } from './DemoContent';
import { UsageGuide } from './UsageGuide';

export const WelcomeScreen: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false);
  const [showGuide, setShowGuide] = useState(false);

  const features = [
    {
      icon: Sparkles,
      title: 'Multiple AI Models',
      description: 'Choose from GPT-4, Claude, and other cutting-edge AI models'
    },
    {
      icon: Code,
      title: 'Live Code Previews',
      description: 'Interactive HTML, CSS, and JavaScript examples with real-time rendering'
    },
    {
      icon: Palette,
      title: 'Interactive Demos',
      description: 'Hands-on demonstrations with adjustable parameters'
    },
    {
      icon: BarChart3,
      title: 'Visual Diagrams',
      description: 'Charts, flowcharts, and mathematical expressions'
    },
    {
      icon: Zap,
      title: 'Rich Formatting',
      description: 'Enhanced markdown with syntax highlighting and beautiful typography'
    },
    {
      icon: Shield,
      title: 'Privacy First',
      description: 'Your conversations are encrypted and secure'
    }
  ];

  if (showDemo) {
    return <DemoContent />;
  }

  if (showGuide) {
    return <UsageGuide />;
  }

  return (
    <div className="flex flex-col items-center justify-center h-full max-w-4xl mx-auto text-center">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-white mb-4">
          Welcome to <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Chat Wizard</span>
        </h1>
        <p className="text-xl text-white/70 max-w-2xl mb-6">
          Your intelligent AI companion with enhanced visual responses, interactive code examples,
          and rich formatting capabilities.
        </p>

        <div className="flex gap-4 justify-center">
          <Button
            onClick={() => setShowDemo(true)}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2"
          >
            <Eye className="h-4 w-4 mr-2" />
            See Features Demo
          </Button>
          <Button
            onClick={() => setShowGuide(true)}
            variant="outline"
            className="border-blue-500/30 text-blue-300 hover:bg-blue-500/10 px-6 py-2"
          >
            <BookOpen className="h-4 w-4 mr-2" />
            How to Use
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-5xl mb-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="bg-white/5 backdrop-blur-lg rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300"
          >
            <feature.icon className="h-8 w-8 text-blue-400 mb-3 mx-auto" />
            <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
            <p className="text-white/60 text-sm">{feature.description}</p>
          </div>
        ))}
      </div>

      <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-4 max-w-2xl">
        <h3 className="text-white font-semibold mb-2">✨ Try These Enhanced Features:</h3>
        <div className="text-white/70 text-sm space-y-1">
          <p>• "Show me an interactive HTML example"</p>
          <p>• "Create a CSS animation demo"</p>
          <p>• "Explain flexbox with a visual diagram"</p>
          <p>• "Generate a color picker interface"</p>
        </div>
      </div>

      <div className="text-white/40 text-sm mt-6">
        Type your message below to start a conversation
      </div>
    </div>
  );
};
