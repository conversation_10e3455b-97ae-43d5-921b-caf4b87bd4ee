import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Play, Pause, RotateCcw, FastForward, Rewind } from 'lucide-react';
import { MOLECULES, REACTIONS, MolecularUtils, type Molecule, type ChemicalReaction } from '@/lib/molecularData';

interface ChemicalReactionProps {
  reactionId?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  animationDuration?: number;
}

export const ChemicalReaction: React.FC<ChemicalReactionProps> = ({
  reactionId = 'carbonicAcidFormation',
  autoPlay = true,
  showControls = true,
  animationDuration = 8000
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [progress, setProgress] = useState(0);
  const [speed, setSpeed] = useState(1);
  const [currentPhase, setCurrentPhase] = useState<'reactants' | 'transition' | 'products'>('reactants');
  const [molecules, setMolecules] = useState<Molecule[]>([]);

  const reaction = REACTIONS[reactionId];
  
  // Animation phases
  const phases = {
    reactants: { start: 0, end: 0.3 },
    transition: { start: 0.3, end: 0.7 },
    products: { start: 0.7, end: 1.0 }
  };

  // Initialize molecules based on reaction
  useEffect(() => {
    if (!reaction) return;

    const reactantMolecules: Molecule[] = [];
    const productMolecules: Molecule[] = [];

    // Position reactants
    reaction.reactants.forEach((reactantId, index) => {
      const molecule = MOLECULES[reactantId];
      if (molecule) {
        const cloned = MolecularUtils.cloneMolecule(molecule, {
          x: -150 + (index * 100),
          y: 0,
          z: 0
        });
        reactantMolecules.push(cloned);
      }
    });

    // Position products
    reaction.products.forEach((productId, index) => {
      const molecule = MOLECULES[productId];
      if (molecule) {
        const cloned = MolecularUtils.cloneMolecule(molecule, {
          x: 150 + (index * 100),
          y: 0,
          z: 0
        });
        productMolecules.push(cloned);
      }
    });

    setMolecules([...reactantMolecules, ...productMolecules]);
  }, [reactionId, reaction]);

  // Animation loop
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = (prev + (speed * 16) / animationDuration) % 1;
        
        // Update current phase
        if (newProgress < phases.transition.start) {
          setCurrentPhase('reactants');
        } else if (newProgress < phases.products.start) {
          setCurrentPhase('transition');
        } else {
          setCurrentPhase('products');
        }
        
        return newProgress;
      });
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, [isPlaying, speed, animationDuration]);

  // 3D projection function
  const project3D = (x: number, y: number, z: number, time: number = 0) => {
    const centerX = 300;
    const centerY = 200;
    
    // Add rotation based on time
    const rotY = time * 0.5;
    const rotX = Math.sin(time * 0.3) * 0.2;
    
    // Apply rotation
    const cosY = Math.cos(rotY);
    const sinY = Math.sin(rotY);
    const cosX = Math.cos(rotX);
    const sinX = Math.sin(rotX);
    
    const rotatedX = x * cosY - z * sinY;
    const rotatedZ = x * sinY + z * cosY;
    const rotatedY = y * cosX - rotatedZ * sinX;
    const finalZ = y * sinX + rotatedZ * cosX;
    
    // Apply perspective
    const perspective = 500;
    const scale = perspective / (perspective + finalZ);
    
    return {
      x: centerX + rotatedX * scale,
      y: centerY + rotatedY * scale,
      scale: scale
    };
  };

  // Calculate molecule position based on animation progress
  const getMoleculePosition = (molecule: Molecule, index: number): { x: number; y: number; z: number } => {
    const isReactant = reaction?.reactants.some(id => molecule.id.includes(id));
    const basePos = molecule.position;
    
    if (currentPhase === 'reactants') {
      return basePos;
    } else if (currentPhase === 'transition') {
      // Move towards center with some chaos
      const transitionProgress = (progress - phases.transition.start) / (phases.transition.end - phases.transition.start);
      const chaos = Math.sin(transitionProgress * Math.PI * 4) * 20;
      
      return {
        x: basePos.x + (0 - basePos.x) * transitionProgress + chaos,
        y: basePos.y + Math.sin(transitionProgress * Math.PI * 2) * 30,
        z: basePos.z + Math.cos(transitionProgress * Math.PI * 3) * 20
      };
    } else {
      // Products phase - move to final positions
      const productProgress = (progress - phases.products.start) / (phases.products.end - phases.products.start);
      const targetX = isReactant ? 400 : basePos.x; // Reactants move off-screen
      
      return {
        x: basePos.x + (targetX - basePos.x) * productProgress,
        y: basePos.y,
        z: basePos.z
      };
    }
  };

  // Render atom with 3D effects
  const renderAtom = (atom: any, molPos: { x: number; y: number; z: number }, opacity: number = 1) => {
    const worldPos = {
      x: atom.x + molPos.x,
      y: atom.y + molPos.y,
      z: atom.z + molPos.z
    };
    
    const projected = project3D(worldPos.x, worldPos.y, worldPos.z, progress * Math.PI * 2);
    const radius = atom.radius * projected.scale;
    
    return (
      <g key={atom.id} opacity={opacity}>
        <defs>
          <radialGradient id={`grad-${atom.id}`} cx="30%" cy="30%">
            <stop offset="0%" stopColor={atom.color} stopOpacity="1" />
            <stop offset="70%" stopColor={atom.color} stopOpacity="0.8" />
            <stop offset="100%" stopColor="#000000" stopOpacity="0.3" />
          </radialGradient>
          <filter id={`glow-${atom.id}`}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        <circle
          cx={projected.x}
          cy={projected.y}
          r={radius}
          fill={`url(#grad-${atom.id})`}
          filter={currentPhase === 'transition' ? `url(#glow-${atom.id})` : undefined}
          stroke="#ffffff"
          strokeWidth={0.5}
        />
        <text
          x={projected.x}
          y={projected.y + 3}
          textAnchor="middle"
          fontSize={Math.max(8, 10 * projected.scale)}
          fill="#ffffff"
          fontWeight="bold"
          style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
        >
          {atom.element}
        </text>
      </g>
    );
  };

  // Render bond
  const renderBond = (bond: any, molecule: Molecule, molPos: { x: number; y: number; z: number }, opacity: number = 1) => {
    const atom1 = molecule.atoms.find(a => a.id === bond.atom1);
    const atom2 = molecule.atoms.find(a => a.id === bond.atom2);
    
    if (!atom1 || !atom2) return null;
    
    const pos1 = project3D(atom1.x + molPos.x, atom1.y + molPos.y, atom1.z + molPos.z, progress * Math.PI * 2);
    const pos2 = project3D(atom2.x + molPos.x, atom2.y + molPos.y, atom2.z + molPos.z, progress * Math.PI * 2);
    
    const bondWidth = bond.type === 'single' ? 2 : bond.type === 'double' ? 4 : 6;
    
    return (
      <line
        key={bond.id}
        x1={pos1.x}
        y1={pos1.y}
        x2={pos2.x}
        y2={pos2.y}
        stroke="#ffffff"
        strokeWidth={bondWidth * Math.min(pos1.scale, pos2.scale)}
        opacity={opacity * 0.8}
        style={{ filter: 'drop-shadow(1px 1px 2px rgba(0,0,0,0.5))' }}
      />
    );
  };

  const togglePlayPause = () => setIsPlaying(!isPlaying);
  const resetAnimation = () => {
    setProgress(0);
    setCurrentPhase('reactants');
  };
  const skipToPhase = (phase: 'reactants' | 'transition' | 'products') => {
    setProgress(phases[phase].start);
    setCurrentPhase(phase);
  };

  if (!reaction) {
    return <div className="text-red-400">Reaction not found: {reactionId}</div>;
  }

  return (
    <Card className="w-full bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div>
            <span>⚗️ {reaction.name}</span>
            <div className="text-sm font-normal text-slate-300 mt-1">
              {reaction.equation}
            </div>
          </div>
          {showControls && (
            <div className="flex gap-2">
              <Button variant="ghost" size="sm" onClick={() => skipToPhase('reactants')}>
                <Rewind className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="sm" onClick={() => skipToPhase('products')}>
                <FastForward className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={resetAnimation}>
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 3D Animation Viewport */}
          <div className="bg-gradient-to-br from-slate-900 to-slate-800 rounded-lg p-4 border border-slate-600">
            <svg
              ref={svgRef}
              width="600"
              height="400"
              viewBox="0 0 600 400"
              className="w-full h-auto border border-slate-700 rounded"
              style={{ background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)' }}
            >
              {/* Phase indicator */}
              <text x="20" y="30" fill="#ffffff" fontSize="14" fontWeight="bold">
                Phase: {currentPhase.charAt(0).toUpperCase() + currentPhase.slice(1)}
              </text>
              
              {/* Energy indicator */}
              <text x="20" y="50" fill="#ffff00" fontSize="12">
                ΔH = {reaction.energyChange} kJ/mol
              </text>
              
              {/* Render molecules */}
              {molecules.map((molecule, index) => {
                const molPos = getMoleculePosition(molecule, index);
                const isReactant = reaction.reactants.some(id => molecule.id.includes(id));
                
                // Calculate opacity based on phase
                let opacity = 1;
                if (currentPhase === 'transition') {
                  opacity = 0.7 + Math.sin(progress * Math.PI * 8) * 0.3;
                } else if (currentPhase === 'products' && isReactant) {
                  const fadeProgress = (progress - phases.products.start) / (phases.products.end - phases.products.start);
                  opacity = 1 - fadeProgress;
                }
                
                return (
                  <g key={molecule.id}>
                    {/* Render bonds first */}
                    {molecule.bonds.map(bond => renderBond(bond, molecule, molPos, opacity))}
                    {/* Render atoms */}
                    {molecule.atoms.map(atom => renderAtom(atom, molPos, opacity))}
                  </g>
                );
              })}
              
              {/* Reaction arrow */}
              <g opacity={currentPhase === 'transition' ? 1 : 0.3}>
                <defs>
                  <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                          refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#ffff00" />
                  </marker>
                </defs>
                <line x1="250" y1="200" x2="350" y2="200" 
                      stroke="#ffff00" strokeWidth="3" 
                      markerEnd="url(#arrowhead)" />
                <text x="300" y="190" textAnchor="middle" fill="#ffff00" fontSize="12">
                  {currentPhase === 'transition' ? 'REACTING' : 'REACTION'}
                </text>
              </g>
            </svg>
          </div>

          {/* Controls */}
          {showControls && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label className="text-white">Animation Speed</Label>
                  <Slider
                    value={[speed]}
                    onValueChange={(value) => setSpeed(value[0])}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                  <span className="text-sm text-slate-400">{speed.toFixed(1)}x</span>
                </div>
                
                <div>
                  <Label className="text-white">Progress</Label>
                  <Slider
                    value={[progress]}
                    onValueChange={(value) => setProgress(value[0])}
                    min={0}
                    max={1}
                    step={0.01}
                    className="mt-2"
                  />
                  <span className="text-sm text-slate-400">{(progress * 100).toFixed(0)}%</span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="text-sm text-slate-300">
                  <p><strong>Type:</strong> {reaction.type}</p>
                  <p><strong>Energy Change:</strong> {reaction.energyChange} kJ/mol</p>
                  <p><strong>Current Phase:</strong> {currentPhase}</p>
                </div>
                
                <div className="text-xs text-slate-400">
                  <p>{reaction.description}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
