import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Zap, 
  TrendingUp,
  TrendingDown,
  Monitor,
  Clock,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Gauge
} from 'lucide-react';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  status: 'good' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  history: number[];
}

interface OptimizationSuggestion {
  id: string;
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  category: 'memory' | 'cpu' | 'network' | 'rendering';
  implemented: boolean;
}

export const PerformanceMonitor: React.FC = () => {
  const [activeTab, setActiveTab] = useState('metrics');
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  const initialSuggestions: OptimizationSuggestion[] = [
    {
      id: 'opt-001',
      title: 'Implement Virtual Scrolling',
      description: 'Use virtual scrolling for large lists to improve rendering performance',
      impact: 'high',
      effort: 'medium',
      category: 'rendering',
      implemented: false
    },
    {
      id: 'opt-002',
      title: 'Lazy Load Components',
      description: 'Implement lazy loading for heavy components to reduce initial bundle size',
      impact: 'medium',
      effort: 'low',
      category: 'network',
      implemented: true
    },
    {
      id: 'opt-003',
      title: 'Optimize Animation Frames',
      description: 'Use requestAnimationFrame for smoother animations and better performance',
      impact: 'medium',
      effort: 'medium',
      category: 'cpu',
      implemented: true
    },
    {
      id: 'opt-004',
      title: 'Memory Cleanup',
      description: 'Implement proper cleanup for event listeners and intervals',
      impact: 'high',
      effort: 'low',
      category: 'memory',
      implemented: true
    },
    {
      id: 'opt-005',
      title: 'Code Splitting',
      description: 'Split code into smaller chunks for better loading performance',
      impact: 'high',
      effort: 'high',
      category: 'network',
      implemented: false
    },
    {
      id: 'opt-006',
      title: 'Debounce User Inputs',
      description: 'Debounce search and input handlers to reduce unnecessary computations',
      impact: 'medium',
      effort: 'low',
      category: 'cpu',
      implemented: true
    }
  ];

  useEffect(() => {
    setSuggestions(initialSuggestions);
  }, []);

  // Simulate performance monitoring
  useEffect(() => {
    if (isMonitoring) {
      intervalRef.current = setInterval(() => {
        updateMetrics();
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isMonitoring]);

  const updateMetrics = () => {
    const newMetrics: PerformanceMetric[] = [
      {
        name: 'Frame Rate',
        value: 58 + Math.random() * 4,
        unit: 'FPS',
        status: 'good',
        trend: 'stable',
        history: []
      },
      {
        name: 'Memory Usage',
        value: 45 + Math.random() * 10,
        unit: 'MB',
        status: 'good',
        trend: 'up',
        history: []
      },
      {
        name: 'CPU Usage',
        value: 15 + Math.random() * 20,
        unit: '%',
        status: 'good',
        trend: 'down',
        history: []
      },
      {
        name: 'Render Time',
        value: 12 + Math.random() * 8,
        unit: 'ms',
        status: 'good',
        trend: 'stable',
        history: []
      },
      {
        name: 'Bundle Size',
        value: 2.4,
        unit: 'MB',
        status: 'warning',
        trend: 'stable',
        history: []
      },
      {
        name: 'Load Time',
        value: 1.2 + Math.random() * 0.3,
        unit: 's',
        status: 'good',
        trend: 'down',
        history: []
      }
    ];

    // Update history for existing metrics
    setMetrics(prevMetrics => {
      return newMetrics.map(newMetric => {
        const existing = prevMetrics.find(m => m.name === newMetric.name);
        if (existing) {
          const history = [...existing.history, newMetric.value].slice(-20);
          return { ...newMetric, history };
        }
        return { ...newMetric, history: [newMetric.value] };
      });
    });
  };

  const getStatusColor = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'good':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'critical':
        return 'text-red-400';
    }
  };

  const getStatusIcon = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-400" />;
    }
  };

  const getTrendIcon = (trend: PerformanceMetric['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-400" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-green-400" />;
      case 'stable':
        return <Activity className="h-4 w-4 text-slate-400" />;
    }
  };

  const getImpactColor = (impact: OptimizationSuggestion['impact']) => {
    switch (impact) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
    }
  };

  const getEffortColor = (effort: OptimizationSuggestion['effort']) => {
    switch (effort) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
    }
  };

  const getCategoryIcon = (category: OptimizationSuggestion['category']) => {
    switch (category) {
      case 'memory':
        return <HardDrive className="h-4 w-4" />;
      case 'cpu':
        return <Cpu className="h-4 w-4" />;
      case 'network':
        return <Zap className="h-4 w-4" />;
      case 'rendering':
        return <Monitor className="h-4 w-4" />;
    }
  };

  const toggleOptimization = (id: string) => {
    setSuggestions(prev => prev.map(suggestion => 
      suggestion.id === id 
        ? { ...suggestion, implemented: !suggestion.implemented }
        : suggestion
    ));
  };

  const getOverallScore = () => {
    const goodMetrics = metrics.filter(m => m.status === 'good').length;
    const totalMetrics = metrics.length;
    return totalMetrics > 0 ? Math.round((goodMetrics / totalMetrics) * 100) : 0;
  };

  const implementedOptimizations = suggestions.filter(s => s.implemented).length;
  const totalOptimizations = suggestions.length;
  const optimizationScore = totalOptimizations > 0 ? Math.round((implementedOptimizations / totalOptimizations) * 100) : 0;

  return (
    <Card className="w-full max-w-6xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <Gauge className="h-6 w-6 text-blue-400" />
            Performance Monitor
            <Badge variant="secondary" className="ml-2">
              Score: {getOverallScore()}%
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMonitoring(!isMonitoring)}
              className={isMonitoring ? 'text-green-400' : 'text-slate-400'}
            >
              {isMonitoring ? 'Monitoring' : 'Paused'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overall Performance Score */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Performance Score
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-300">Overall Health</span>
                  <span className="text-white">{getOverallScore()}%</span>
                </div>
                <Progress value={getOverallScore()} className="w-full" />
              </div>
            </div>
            
            <div className="bg-slate-700/50 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Optimization Score
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-300">Implemented</span>
                  <span className="text-white">{implementedOptimizations}/{totalOptimizations}</span>
                </div>
                <Progress value={optimizationScore} className="w-full" />
              </div>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-700">
              <TabsTrigger value="metrics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Metrics
              </TabsTrigger>
              <TabsTrigger value="optimizations" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Optimizations
              </TabsTrigger>
              <TabsTrigger value="insights" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Insights
              </TabsTrigger>
            </TabsList>

            <TabsContent value="metrics" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {metrics.map((metric, index) => (
                  <div key={index} className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">{metric.name}</h4>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(metric.status)}
                        {getTrendIcon(metric.trend)}
                      </div>
                    </div>
                    <div className="flex items-baseline gap-1">
                      <span className={`text-2xl font-bold ${getStatusColor(metric.status)}`}>
                        {metric.value.toFixed(1)}
                      </span>
                      <span className="text-sm text-slate-400">{metric.unit}</span>
                    </div>
                    {metric.history.length > 1 && (
                      <div className="mt-2 h-8 flex items-end gap-1">
                        {metric.history.slice(-10).map((value, i) => (
                          <div
                            key={i}
                            className="bg-blue-400 rounded-sm flex-1"
                            style={{
                              height: `${(value / Math.max(...metric.history)) * 100}%`,
                              minHeight: '2px'
                            }}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="optimizations" className="mt-6">
              <div className="space-y-4">
                {suggestions.map((suggestion) => (
                  <div key={suggestion.id} className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(suggestion.category)}
                        <div>
                          <h4 className="text-white font-medium flex items-center gap-2">
                            {suggestion.title}
                            {suggestion.implemented && (
                              <CheckCircle className="h-4 w-4 text-green-400" />
                            )}
                          </h4>
                          <p className="text-sm text-slate-400">{suggestion.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${getImpactColor(suggestion.impact)}`}></div>
                          <span className="text-xs text-slate-400">Impact</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${getEffortColor(suggestion.effort)}`}></div>
                          <span className="text-xs text-slate-400">Effort</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleOptimization(suggestion.id)}
                          className={suggestion.implemented ? 'bg-green-500/20 border-green-500/50' : ''}
                        >
                          {suggestion.implemented ? 'Implemented' : 'Implement'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="insights" className="mt-6">
              <div className="space-y-6">
                <div className="bg-slate-700/50 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Performance Insights
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                      <div>
                        <h4 className="text-white font-medium">Excellent Frame Rate</h4>
                        <p className="text-sm text-slate-400">
                          Your application maintains a consistent 60 FPS, providing smooth user experience.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5" />
                      <div>
                        <h4 className="text-white font-medium">Bundle Size Optimization</h4>
                        <p className="text-sm text-slate-400">
                          Consider implementing code splitting to reduce the initial bundle size from 2.4MB.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                      <div>
                        <h4 className="text-white font-medium">Memory Management</h4>
                        <p className="text-sm text-slate-400">
                          Memory usage is well-controlled with proper cleanup implementations.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-slate-700/50 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Recommendations
                  </h3>
                  <div className="space-y-2">
                    <div className="text-sm text-slate-300">
                      • Implement virtual scrolling for large molecular datasets
                    </div>
                    <div className="text-sm text-slate-300">
                      • Consider using Web Workers for heavy computational tasks
                    </div>
                    <div className="text-sm text-slate-300">
                      • Optimize SVG rendering for complex molecular structures
                    </div>
                    <div className="text-sm text-slate-300">
                      • Cache frequently accessed molecular data
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
};
