import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check, Play, Eye, EyeOff } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';

interface CodeBlockProps {
  code: string;
  language: string;
  showLineNumbers?: boolean;
  title?: string;
  editable?: boolean;
  onCodeChange?: (code: string) => void;
}

export const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language,
  showLineNumbers = false,
  title,
  editable = false,
  onCodeChange,
}) => {
  const [copied, setCopied] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [editableCode, setEditableCode] = useState(code);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(editableCode);
      setCopied(true);
      toast.success('Code copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error('Failed to copy code');
    }
  };

  const handleCodeEdit = (newCode: string) => {
    setEditableCode(newCode);
    onCodeChange?.(newCode);
  };

  const getLanguageDisplayName = (lang: string) => {
    const languageMap: Record<string, string> = {
      js: 'JavaScript',
      ts: 'TypeScript',
      jsx: 'React JSX',
      tsx: 'React TSX',
      py: 'Python',
      rb: 'Ruby',
      go: 'Go',
      rs: 'Rust',
      cpp: 'C++',
      c: 'C',
      java: 'Java',
      php: 'PHP',
      sh: 'Shell',
      bash: 'Bash',
      sql: 'SQL',
      json: 'JSON',
      xml: 'XML',
      yaml: 'YAML',
      yml: 'YAML',
      md: 'Markdown',
      css: 'CSS',
      scss: 'SCSS',
      sass: 'Sass',
      less: 'Less',
      html: 'HTML',
      vue: 'Vue',
      svelte: 'Svelte',
    };
    return languageMap[lang] || lang.toUpperCase();
  };

  const customStyle = {
    ...oneDark,
    'pre[class*="language-"]': {
      ...oneDark['pre[class*="language-"]'],
      background: 'rgba(15, 23, 42, 0.8)',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(148, 163, 184, 0.2)',
      borderRadius: '12px',
      margin: 0,
      padding: '1rem',
    },
    'code[class*="language-"]': {
      ...oneDark['code[class*="language-"]'],
      background: 'transparent',
    },
  };

  return (
    <div className="my-6 group">
      {/* Header */}
      <div className="flex items-center justify-between bg-slate-800/80 backdrop-blur-sm border border-slate-600 rounded-t-lg px-4 py-2">
        <div className="flex items-center gap-3">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <span className="text-sm font-medium text-slate-300">
            {title || getLanguageDisplayName(language)}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Collapse/Expand button for long code */}
          {editableCode.split('\n').length > 10 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0 text-slate-400 hover:text-white"
            >
              {isCollapsed ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
          )}
          
          {/* Copy button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-8 w-8 p-0 text-slate-400 hover:text-white"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Code content */}
      {!isCollapsed && (
        <div className="relative">
          {editable ? (
            <textarea
              value={editableCode}
              onChange={(e) => handleCodeEdit(e.target.value)}
              className="w-full bg-slate-900/80 backdrop-blur-sm border border-slate-600 border-t-0 rounded-b-lg p-4 font-mono text-sm text-slate-200 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={Math.min(editableCode.split('\n').length + 1, 20)}
              spellCheck={false}
            />
          ) : (
            <SyntaxHighlighter
              language={language}
              style={customStyle}
              showLineNumbers={showLineNumbers}
              customStyle={{
                margin: 0,
                borderRadius: '0 0 12px 12px',
                borderTop: 'none',
              }}
              lineNumberStyle={{
                color: '#64748b',
                fontSize: '0.875rem',
                paddingRight: '1rem',
              }}
            >
              {editableCode}
            </SyntaxHighlighter>
          )}
        </div>
      )}

      {/* Collapsed state */}
      {isCollapsed && (
        <div className="bg-slate-900/80 backdrop-blur-sm border border-slate-600 border-t-0 rounded-b-lg p-4">
          <div className="text-slate-400 text-sm italic">
            Code collapsed ({editableCode.split('\n').length} lines)
          </div>
        </div>
      )}
    </div>
  );
};
