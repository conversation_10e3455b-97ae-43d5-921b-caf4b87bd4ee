import React, { useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Maximize2 } from 'lucide-react';

interface VisualDiagramProps {
  type: 'mermaid' | 'flowchart' | 'chart' | 'svg';
  content: string;
  title?: string;
}

export const VisualDiagram: React.FC<VisualDiagramProps> = ({
  type,
  content,
  title,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (type === 'mermaid' && containerRef.current) {
      renderMermaidDiagram();
    } else if (type === 'chart') {
      renderChart();
    } else if (type === 'svg') {
      renderSVG();
    }
  }, [type, content]);

  const renderMermaidDiagram = async () => {
    // For now, we'll create a placeholder since mermaid requires additional setup
    // In a real implementation, you would import and configure mermaid
    if (containerRef.current) {
      containerRef.current.innerHTML = `
        <div class="bg-white rounded-lg p-6 text-center">
          <div class="text-gray-600 mb-4">Mermaid Diagram</div>
          <div class="bg-gray-100 p-4 rounded font-mono text-sm text-left">
            ${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}
          </div>
          <div class="mt-4 text-sm text-gray-500">
            Mermaid rendering would be implemented here
          </div>
        </div>
      `;
    }
  };

  const renderChart = () => {
    if (!containerRef.current) return;

    // Simple bar chart implementation
    const data = [
      { label: 'A', value: 30 },
      { label: 'B', value: 80 },
      { label: 'C', value: 45 },
      { label: 'D', value: 60 },
      { label: 'E', value: 20 },
    ];

    const maxValue = Math.max(...data.map(d => d.value));
    const chartHeight = 200;
    const chartWidth = 400;
    const barWidth = chartWidth / data.length - 10;

    const svg = `
      <svg width="${chartWidth}" height="${chartHeight + 50}" class="bg-white rounded-lg">
        <defs>
          <linearGradient id="barGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
          </linearGradient>
        </defs>
        ${data.map((d, i) => {
          const barHeight = (d.value / maxValue) * chartHeight;
          const x = i * (barWidth + 10) + 20;
          const y = chartHeight - barHeight + 20;
          
          return `
            <rect x="${x}" y="${y}" width="${barWidth}" height="${barHeight}" 
                  fill="url(#barGradient)" rx="4" />
            <text x="${x + barWidth/2}" y="${chartHeight + 40}" 
                  text-anchor="middle" fill="#374151" font-size="14">${d.label}</text>
            <text x="${x + barWidth/2}" y="${y - 5}" 
                  text-anchor="middle" fill="#374151" font-size="12">${d.value}</text>
          `;
        }).join('')}
      </svg>
    `;

    containerRef.current.innerHTML = svg;
  };

  const renderSVG = () => {
    if (!containerRef.current) return;

    // Example: Simple flowchart
    const svg = `
      <svg width="500" height="300" class="bg-white rounded-lg">
        <defs>
          <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                  refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
          </marker>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000020"/>
          </filter>
        </defs>
        
        <!-- Start node -->
        <ellipse cx="100" cy="50" rx="60" ry="30" fill="#10b981" filter="url(#shadow)"/>
        <text x="100" y="55" text-anchor="middle" fill="white" font-weight="bold">Start</text>
        
        <!-- Process node -->
        <rect x="50" y="120" width="100" height="60" rx="10" fill="#3b82f6" filter="url(#shadow)"/>
        <text x="100" y="155" text-anchor="middle" fill="white" font-weight="bold">Process</text>
        
        <!-- Decision node -->
        <polygon points="100,220 150,250 100,280 50,250" fill="#f59e0b" filter="url(#shadow)"/>
        <text x="100" y="255" text-anchor="middle" fill="white" font-weight="bold">Decision</text>
        
        <!-- End nodes -->
        <ellipse cx="250" cy="250" rx="60" ry="30" fill="#ef4444" filter="url(#shadow)"/>
        <text x="250" y="255" text-anchor="middle" fill="white" font-weight="bold">End A</text>
        
        <ellipse cx="400" cy="150" rx="60" ry="30" fill="#10b981" filter="url(#shadow)"/>
        <text x="400" y="155" text-anchor="middle" fill="white" font-weight="bold">End B</text>
        
        <!-- Arrows -->
        <line x1="100" y1="80" x2="100" y2="120" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
        <line x1="100" y1="180" x2="100" y2="220" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
        <line x1="150" y1="250" x2="190" y2="250" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
        <line x1="150" y1="240" x2="340" y2="160" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
        
        <!-- Labels -->
        <text x="170" y="245" fill="#374151" font-size="12">No</text>
        <text x="220" y="200" fill="#374151" font-size="12">Yes</text>
      </svg>
    `;

    containerRef.current.innerHTML = svg;
  };

  const downloadSVG = () => {
    if (!containerRef.current) return;
    
    const svg = containerRef.current.querySelector('svg');
    if (svg) {
      const svgData = new XMLSerializer().serializeToString(svg);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `diagram-${Date.now()}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);
    }
  };

  return (
    <Card className="my-6 bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <span>{title || `${type.charAt(0).toUpperCase() + type.slice(1)} Diagram`}</span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={downloadSVG}
              className="h-8 text-slate-400 hover:text-white"
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-slate-400 hover:text-white"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={containerRef}
          className="flex justify-center items-center min-h-[200px] bg-gradient-to-br from-slate-50 to-slate-100 rounded-lg p-4"
        />
        {type === 'mermaid' && (
          <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <p className="text-sm text-blue-300">
              💡 <strong>Tip:</strong> This is a placeholder for Mermaid diagrams. 
              In a full implementation, this would render interactive Mermaid diagrams.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
