
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Settings, LogOut, CreditCard, HelpCircle } from 'lucide-react';
import { usePuterAuth } from '@/hooks/usePuterAuth';

export const UserProfile: React.FC = () => {
  const { isSignedIn, user, signIn, isLoading } = usePuterAuth();

  if (isLoading) {
    return (
      <div className="space-y-3">
        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg">
          <div className="w-10 h-10 bg-white/20 rounded-full animate-pulse" />
          <div className="flex-1 min-w-0">
            <div className="h-4 bg-white/20 rounded animate-pulse mb-1" />
            <div className="h-3 bg-white/10 rounded animate-pulse w-2/3" />
          </div>
        </div>
        <div className="text-xs text-white/50 text-center">
          Connecting to Puter...
        </div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="space-y-3">
        <Button
          onClick={signIn}
          className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white"
        >
          Sign In to Puter
        </Button>
        <div className="text-xs text-white/50 text-center">
          Sign in to save your chats and preferences
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg">
        <Avatar className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500">
          <AvatarFallback className="text-white font-medium">
            {user?.username?.charAt(0).toUpperCase() || 'U'}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-white">{user?.username || 'User'}</div>
          <div className="text-xs text-white/50">
            {user?.email_confirmed ? 'Verified' : 'Unverified'}
          </div>
        </div>
      </div>
      
      <div className="space-y-1">
        <Button
          variant="ghost"
          className="w-full justify-start text-white/70 hover:text-white hover:bg-white/10"
        >
          <Settings className="h-4 w-4 mr-3" />
          Settings
        </Button>
        
        <Button
          variant="ghost"
          className="w-full justify-start text-white/70 hover:text-white hover:bg-white/10"
        >
          <CreditCard className="h-4 w-4 mr-3" />
          Subscription
        </Button>
        
        <Button
          variant="ghost"
          className="w-full justify-start text-white/70 hover:text-white hover:bg-white/10"
        >
          <HelpCircle className="h-4 w-4 mr-3" />
          Help & Support
        </Button>
        
        <div className="border-t border-white/20 pt-2 mt-2">
          <Button
            variant="ghost"
            className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-500/10"
          >
            <LogOut className="h-4 w-4 mr-3" />
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );
};
