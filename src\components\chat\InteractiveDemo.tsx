import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, RotateCcw, Settings } from 'lucide-react';

interface InteractiveDemoProps {
  content: string;
}

export const InteractiveDemo: React.FC<InteractiveDemoProps> = ({ content }) => {
  const [demoType, setDemoType] = useState<string>('');
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [result, setResult] = useState<any>(null);

  // Parse the content to determine demo type and extract parameters
  useEffect(() => {
    if (content.includes('<!-- INTERACTIVE:CSS_ANIMATION -->')) {
      setDemoType('css_animation');
      setParameters({
        duration: 2,
        delay: 0,
        iterations: 1,
        direction: 'normal',
        timingFunction: 'ease',
      });
    } else if (content.includes('<!-- INTERACTIVE:COLOR_PICKER -->')) {
      setDemoType('color_picker');
      setParameters({
        hue: 200,
        saturation: 70,
        lightness: 50,
        alpha: 1,
      });
    } else if (content.includes('<!-- INTERACTIVE:LAYOUT_DEMO -->')) {
      setDemoType('layout_demo');
      setParameters({
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16,
      });
    } else if (content.includes('<!-- INTERACTIVE:CHART_DEMO -->')) {
      setDemoType('chart_demo');
      setParameters({
        data: [10, 20, 30, 40, 50],
        chartType: 'bar',
        color: '#3b82f6',
      });
    }
  }, [content]);

  const updateParameter = (key: string, value: any) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  };

  const resetParameters = () => {
    if (demoType === 'css_animation') {
      setParameters({
        duration: 2,
        delay: 0,
        iterations: 1,
        direction: 'normal',
        timingFunction: 'ease',
      });
    } else if (demoType === 'color_picker') {
      setParameters({
        hue: 200,
        saturation: 70,
        lightness: 50,
        alpha: 1,
      });
    }
  };

  const CSSAnimationDemo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="duration">Duration (seconds)</Label>
          <Slider
            id="duration"
            min={0.1}
            max={5}
            step={0.1}
            value={[parameters.duration]}
            onValueChange={(value) => updateParameter('duration', value[0])}
            className="mt-2"
          />
          <span className="text-sm text-slate-400">{parameters.duration}s</span>
        </div>
        
        <div>
          <Label htmlFor="delay">Delay (seconds)</Label>
          <Slider
            id="delay"
            min={0}
            max={3}
            step={0.1}
            value={[parameters.delay]}
            onValueChange={(value) => updateParameter('delay', value[0])}
            className="mt-2"
          />
          <span className="text-sm text-slate-400">{parameters.delay}s</span>
        </div>
      </div>

      <div className="preview-area bg-slate-900 rounded-lg p-8 flex justify-center items-center min-h-32">
        <div
          className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg"
          style={{
            animation: `bounce ${parameters.duration}s ${parameters.delay}s ${parameters.iterations === -1 ? 'infinite' : parameters.iterations} ${parameters.direction} ${parameters.timingFunction}`,
          }}
        />
      </div>

      <style jsx>{`
        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-20px); }
        }
      `}</style>
    </div>
  );

  const ColorPickerDemo = () => {
    const color = `hsla(${parameters.hue}, ${parameters.saturation}%, ${parameters.lightness}%, ${parameters.alpha})`;
    
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="hue">Hue</Label>
            <Slider
              id="hue"
              min={0}
              max={360}
              value={[parameters.hue]}
              onValueChange={(value) => updateParameter('hue', value[0])}
              className="mt-2"
            />
            <span className="text-sm text-slate-400">{parameters.hue}°</span>
          </div>
          
          <div>
            <Label htmlFor="saturation">Saturation</Label>
            <Slider
              id="saturation"
              min={0}
              max={100}
              value={[parameters.saturation]}
              onValueChange={(value) => updateParameter('saturation', value[0])}
              className="mt-2"
            />
            <span className="text-sm text-slate-400">{parameters.saturation}%</span>
          </div>
          
          <div>
            <Label htmlFor="lightness">Lightness</Label>
            <Slider
              id="lightness"
              min={0}
              max={100}
              value={[parameters.lightness]}
              onValueChange={(value) => updateParameter('lightness', value[0])}
              className="mt-2"
            />
            <span className="text-sm text-slate-400">{parameters.lightness}%</span>
          </div>
          
          <div>
            <Label htmlFor="alpha">Alpha</Label>
            <Slider
              id="alpha"
              min={0}
              max={1}
              step={0.01}
              value={[parameters.alpha]}
              onValueChange={(value) => updateParameter('alpha', value[0])}
              className="mt-2"
            />
            <span className="text-sm text-slate-400">{parameters.alpha}</span>
          </div>
        </div>

        <div className="preview-area bg-slate-900 rounded-lg p-8 flex flex-col items-center gap-4">
          <div
            className="w-32 h-32 rounded-lg border-2 border-white/20"
            style={{ backgroundColor: color }}
          />
          <div className="text-center">
            <div className="font-mono text-sm bg-slate-800 px-3 py-1 rounded">
              {color}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const LayoutDemo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="flexDirection">Flex Direction</Label>
          <select
            id="flexDirection"
            value={parameters.flexDirection}
            onChange={(e) => updateParameter('flexDirection', e.target.value)}
            className="w-full mt-2 bg-slate-800 border border-slate-600 rounded px-3 py-2 text-white"
          >
            <option value="row">Row</option>
            <option value="column">Column</option>
            <option value="row-reverse">Row Reverse</option>
            <option value="column-reverse">Column Reverse</option>
          </select>
        </div>
        
        <div>
          <Label htmlFor="justifyContent">Justify Content</Label>
          <select
            id="justifyContent"
            value={parameters.justifyContent}
            onChange={(e) => updateParameter('justifyContent', e.target.value)}
            className="w-full mt-2 bg-slate-800 border border-slate-600 rounded px-3 py-2 text-white"
          >
            <option value="flex-start">Flex Start</option>
            <option value="center">Center</option>
            <option value="flex-end">Flex End</option>
            <option value="space-between">Space Between</option>
            <option value="space-around">Space Around</option>
            <option value="space-evenly">Space Evenly</option>
          </select>
        </div>
      </div>

      <div className="preview-area bg-slate-900 rounded-lg p-8 min-h-48">
        <div
          className="h-full border-2 border-dashed border-slate-600 rounded flex"
          style={{
            flexDirection: parameters.flexDirection,
            justifyContent: parameters.justifyContent,
            alignItems: parameters.alignItems,
            gap: `${parameters.gap}px`,
          }}
        >
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold"
            >
              {i}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  if (!demoType) {
    return null;
  }

  return (
    <Card className="my-6 bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-white">
          <Play className="h-5 w-5 text-green-400" />
          Interactive Demo
          <Button
            variant="ghost"
            size="sm"
            onClick={resetParameters}
            className="ml-auto h-8 text-slate-400 hover:text-white"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {demoType === 'css_animation' && <CSSAnimationDemo />}
        {demoType === 'color_picker' && <ColorPickerDemo />}
        {demoType === 'layout_demo' && <LayoutDemo />}
      </CardContent>
    </Card>
  );
};
