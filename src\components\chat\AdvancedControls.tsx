import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Palette, 
  Zap, 
  RotateCcw, 
  Download, 
  Eye, 
  Layers,
  Timer,
  Gauge
} from 'lucide-react';

interface ControlParameter {
  id: string;
  label: string;
  type: 'slider' | 'switch' | 'select' | 'color';
  value: any;
  min?: number;
  max?: number;
  step?: number;
  options?: { value: string; label: string }[];
  description?: string;
}

interface AdvancedControlsProps {
  title?: string;
  parameters?: ControlParameter[];
  onParameterChange?: (id: string, value: any) => void;
  onPresetLoad?: (preset: string) => void;
  onExport?: () => void;
  onReset?: () => void;
}

export const AdvancedControls: React.FC<AdvancedControlsProps> = ({
  title = "Advanced Controls",
  parameters = [],
  onParameterChange,
  onPresetLoad,
  onExport,
  onReset
}) => {
  const [activeTab, setActiveTab] = useState('animation');
  const [currentPreset, setCurrentPreset] = useState('default');
  const [isRecording, setIsRecording] = useState(false);

  // Default parameters for different categories
  const defaultParameters: Record<string, ControlParameter[]> = {
    animation: [
      {
        id: 'speed',
        label: 'Animation Speed',
        type: 'slider',
        value: 1,
        min: 0.1,
        max: 5,
        step: 0.1,
        description: 'Controls the overall animation speed'
      },
      {
        id: 'duration',
        label: 'Duration (seconds)',
        type: 'slider',
        value: 4,
        min: 1,
        max: 20,
        step: 0.5,
        description: 'Total animation duration'
      },
      {
        id: 'easing',
        label: 'Easing Function',
        type: 'select',
        value: 'ease-in-out',
        options: [
          { value: 'linear', label: 'Linear' },
          { value: 'ease', label: 'Ease' },
          { value: 'ease-in', label: 'Ease In' },
          { value: 'ease-out', label: 'Ease Out' },
          { value: 'ease-in-out', label: 'Ease In-Out' },
          { value: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)', label: 'Bounce' }
        ],
        description: 'Animation timing function'
      },
      {
        id: 'loop',
        label: 'Loop Animation',
        type: 'switch',
        value: true,
        description: 'Whether animation should repeat'
      }
    ],
    visual: [
      {
        id: 'zoom',
        label: 'Zoom Level',
        type: 'slider',
        value: 1,
        min: 0.5,
        max: 3,
        step: 0.1,
        description: 'Camera zoom level'
      },
      {
        id: 'rotationX',
        label: 'Rotation X',
        type: 'slider',
        value: 0,
        min: -180,
        max: 180,
        step: 5,
        description: 'Rotation around X-axis'
      },
      {
        id: 'rotationY',
        label: 'Rotation Y',
        type: 'slider',
        value: 0,
        min: -180,
        max: 180,
        step: 5,
        description: 'Rotation around Y-axis'
      },
      {
        id: 'perspective',
        label: 'Perspective',
        type: 'slider',
        value: 1000,
        min: 100,
        max: 2000,
        step: 50,
        description: '3D perspective distance'
      },
      {
        id: 'showBonds',
        label: 'Show Bonds',
        type: 'switch',
        value: true,
        description: 'Display chemical bonds'
      },
      {
        id: 'showLabels',
        label: 'Show Labels',
        type: 'switch',
        value: true,
        description: 'Display atom labels'
      }
    ],
    effects: [
      {
        id: 'glowIntensity',
        label: 'Glow Intensity',
        type: 'slider',
        value: 0.5,
        min: 0,
        max: 2,
        step: 0.1,
        description: 'Intensity of glow effects'
      },
      {
        id: 'shadowBlur',
        label: 'Shadow Blur',
        type: 'slider',
        value: 3,
        min: 0,
        max: 10,
        step: 0.5,
        description: 'Shadow blur radius'
      },
      {
        id: 'particleCount',
        label: 'Particle Count',
        type: 'slider',
        value: 20,
        min: 0,
        max: 100,
        step: 5,
        description: 'Number of reaction particles'
      },
      {
        id: 'colorScheme',
        label: 'Color Scheme',
        type: 'select',
        value: 'cpk',
        options: [
          { value: 'cpk', label: 'CPK Standard' },
          { value: 'jmol', label: 'Jmol Colors' },
          { value: 'rasmol', label: 'RasMol Colors' },
          { value: 'custom', label: 'Custom Palette' }
        ],
        description: 'Atomic color scheme'
      },
      {
        id: 'enableParticles',
        label: 'Particle Effects',
        type: 'switch',
        value: false,
        description: 'Enable particle effects during reactions'
      }
    ]
  };

  const presets = {
    default: 'Default Settings',
    educational: 'Educational Mode',
    presentation: 'Presentation Mode',
    scientific: 'Scientific Accuracy',
    artistic: 'Artistic Style'
  };

  const allParameters = parameters.length > 0 ? parameters : [
    ...defaultParameters.animation,
    ...defaultParameters.visual,
    ...defaultParameters.effects
  ];

  const getParametersByCategory = (category: string) => {
    if (parameters.length > 0) {
      return parameters.filter(p => p.id.includes(category));
    }
    return defaultParameters[category] || [];
  };

  const handleParameterChange = (id: string, value: any) => {
    onParameterChange?.(id, value);
  };

  const handlePresetChange = (preset: string) => {
    setCurrentPreset(preset);
    onPresetLoad?.(preset);
  };

  const renderParameter = (param: ControlParameter) => {
    switch (param.type) {
      case 'slider':
        return (
          <div key={param.id} className="space-y-2">
            <div className="flex justify-between items-center">
              <Label className="text-white text-sm">{param.label}</Label>
              <Badge variant="outline" className="text-xs">
                {typeof param.value === 'number' ? param.value.toFixed(1) : param.value}
              </Badge>
            </div>
            <Slider
              value={[param.value]}
              onValueChange={(value) => handleParameterChange(param.id, value[0])}
              min={param.min}
              max={param.max}
              step={param.step}
              className="w-full"
            />
            {param.description && (
              <p className="text-xs text-slate-400">{param.description}</p>
            )}
          </div>
        );

      case 'switch':
        return (
          <div key={param.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-white text-sm">{param.label}</Label>
              <Switch
                checked={param.value}
                onCheckedChange={(checked) => handleParameterChange(param.id, checked)}
              />
            </div>
            {param.description && (
              <p className="text-xs text-slate-400">{param.description}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={param.id} className="space-y-2">
            <Label className="text-white text-sm">{param.label}</Label>
            <Select value={param.value} onValueChange={(value) => handleParameterChange(param.id, value)}>
              <SelectTrigger className="bg-slate-700 border-slate-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {param.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {param.description && (
              <p className="text-xs text-slate-400">{param.description}</p>
            )}
          </div>
        );

      case 'color':
        return (
          <div key={param.id} className="space-y-2">
            <Label className="text-white text-sm">{param.label}</Label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={param.value}
                onChange={(e) => handleParameterChange(param.id, e.target.value)}
                className="w-12 h-8 rounded border border-slate-600"
              />
              <span className="text-slate-300 text-sm font-mono">{param.value}</span>
            </div>
            {param.description && (
              <p className="text-xs text-slate-400">{param.description}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {title}
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsRecording(!isRecording)}
              className={isRecording ? 'text-red-400' : 'text-slate-400'}
            >
              <Timer className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onExport}>
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onReset}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Preset Selection */}
          <div className="space-y-2">
            <Label className="text-white">Presets</Label>
            <Select value={currentPreset} onValueChange={handlePresetChange}>
              <SelectTrigger className="bg-slate-700 border-slate-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(presets).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Categories */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-700">
              <TabsTrigger value="animation" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Animation
              </TabsTrigger>
              <TabsTrigger value="visual" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Visual
              </TabsTrigger>
              <TabsTrigger value="effects" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Effects
              </TabsTrigger>
            </TabsList>

            <TabsContent value="animation" className="mt-6">
              <div className="space-y-4">
                {getParametersByCategory('animation').map(renderParameter)}
              </div>
            </TabsContent>

            <TabsContent value="visual" className="mt-6">
              <div className="space-y-4">
                {getParametersByCategory('visual').map(renderParameter)}
              </div>
            </TabsContent>

            <TabsContent value="effects" className="mt-6">
              <div className="space-y-4">
                {getParametersByCategory('effects').map(renderParameter)}
              </div>
            </TabsContent>
          </Tabs>

          {/* Real-time Stats */}
          <div className="bg-slate-700/50 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2 flex items-center gap-2">
              <Gauge className="h-4 w-4" />
              Performance Stats
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-slate-400">Frame Rate:</span>
                <span className="text-green-400 ml-2">60 FPS</span>
              </div>
              <div>
                <span className="text-slate-400">Render Time:</span>
                <span className="text-blue-400 ml-2">16ms</span>
              </div>
              <div>
                <span className="text-slate-400">Memory Usage:</span>
                <span className="text-yellow-400 ml-2">45 MB</span>
              </div>
              <div>
                <span className="text-slate-400">Active Elements:</span>
                <span className="text-purple-400 ml-2">24</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Save State
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export Config
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
