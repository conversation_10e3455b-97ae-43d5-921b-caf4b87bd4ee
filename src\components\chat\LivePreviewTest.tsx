import React, { useState } from 'react';
import { MessageRenderer } from './MessageRenderer';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

export const LivePreviewTest: React.FC = () => {
  const [selectedTest, setSelectedTest] = useState('molecular');

  const testMessages = {
    molecular: `# 🧬 3D Molecular Animation Test

This should show a live preview of the molecular animation:

\`\`\`html
<div style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); padding: 20px; border-radius: 15px; color: white; text-align: center;">
    <h2>🧬 H₂O Molecule</h2>
    <div style="position: relative; width: 150px; height: 100px; margin: 20px auto; perspective: 1000px;">
        <div style="transform-style: preserve-3d; animation: rotate 4s infinite linear;">
            <!-- Oxygen atom -->
            <div style="position: absolute; width: 40px; height: 40px; background: radial-gradient(circle, #ff4444, #cc0000); border-radius: 50%; left: 55px; top: 30px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">O</div>
            <!-- Hydrogen atoms -->
            <div style="position: absolute; width: 30px; height: 30px; background: radial-gradient(circle, #ffffff, #cccccc); border-radius: 50%; left: 20px; top: 15px; display: flex; align-items: center; justify-content: center; color: #333; font-weight: bold;">H</div>
            <div style="position: absolute; width: 30px; height: 30px; background: radial-gradient(circle, #ffffff, #cccccc); border-radius: 50%; left: 100px; top: 15px; display: flex; align-items: center; justify-content: center; color: #333; font-weight: bold;">H</div>
            <!-- Bonds -->
            <div style="position: absolute; width: 25px; height: 2px; background: white; left: 40px; top: 37px; transform: rotate(-30deg); box-shadow: 0 0 5px rgba(255,255,255,0.5);"></div>
            <div style="position: absolute; width: 25px; height: 2px; background: white; left: 85px; top: 37px; transform: rotate(30deg); box-shadow: 0 0 5px rgba(255,255,255,0.5);"></div>
        </div>
    </div>
    <p>Water molecule rotating in 3D space</p>
</div>

<style>
@keyframes rotate {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    50% { transform: rotateX(180deg) rotateY(180deg); }
    100% { transform: rotateX(360deg) rotateY(360deg); }
}
</style>
\`\`\``,

    interactive: `# 🎮 Interactive HTML Test

This should show an interactive card with live preview:

\`\`\`html
<div id="interactiveCard" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 20px; color: white; text-align: center; max-width: 400px; margin: 20px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3); transition: all 0.3s ease;">
    <h2 style="margin: 0 0 20px 0;">🎨 Interactive Demo</h2>
    <p style="margin: 0 0 20px 0; opacity: 0.9;">Click the buttons to see magic!</p>
    
    <button onclick="changeColor()" style="background: #4CAF50; color: white; border: none; padding: 12px 20px; margin: 5px; border-radius: 25px; cursor: pointer; font-size: 14px;">🎨 Change Color</button>
    
    <button onclick="addSparkle()" style="background: #FF9800; color: white; border: none; padding: 12px 20px; margin: 5px; border-radius: 25px; cursor: pointer; font-size: 14px;">✨ Add Sparkle</button>
    
    <button onclick="resetCard()" style="background: #f44336; color: white; border: none; padding: 12px 20px; margin: 5px; border-radius: 25px; cursor: pointer; font-size: 14px;">🔄 Reset</button>
    
    <div id="sparkleContainer" style="margin-top: 20px; min-height: 50px;"></div>
</div>

<script>
const colors = [
    ['#667eea', '#764ba2'],
    ['#f093fb', '#f5576c'],
    ['#4facfe', '#00f2fe'],
    ['#43e97b', '#38f9d7'],
    ['#fa709a', '#fee140']
];

function changeColor() {
    const card = document.getElementById('interactiveCard');
    const randomPair = colors[Math.floor(Math.random() * colors.length)];
    card.style.background = \`linear-gradient(135deg, \${randomPair[0]} 0%, \${randomPair[1]} 100%)\`;
    card.style.transform = 'scale(1.05)';
    setTimeout(() => {
        card.style.transform = 'scale(1)';
    }, 200);
}

function addSparkle() {
    const container = document.getElementById('sparkleContainer');
    const sparkle = document.createElement('span');
    sparkle.textContent = '✨';
    sparkle.style.cssText = \`
        display: inline-block;
        font-size: 20px;
        animation: sparkle 2s ease-out forwards;
        margin: 0 5px;
    \`;
    container.appendChild(sparkle);
    
    setTimeout(() => {
        if (sparkle.parentNode) {
            sparkle.parentNode.removeChild(sparkle);
        }
    }, 2000);
}

function resetCard() {
    const card = document.getElementById('interactiveCard');
    const container = document.getElementById('sparkleContainer');
    card.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    container.innerHTML = '';
}
</script>

<style>
@keyframes sparkle {
    0% { transform: translateY(0) scale(1); opacity: 1; }
    50% { transform: translateY(-20px) scale(1.5); opacity: 0.8; }
    100% { transform: translateY(-40px) scale(0.5); opacity: 0; }
}
</style>
\`\`\``,

    simple: `# 🌟 Simple HTML Test

A basic test to verify live preview functionality:

\`\`\`html
<div style="background: #4CAF50; color: white; padding: 20px; border-radius: 10px; text-align: center;">
    <h1>✅ Live Preview Working!</h1>
    <p>If you can see this rendered with styling, the live preview is working correctly.</p>
    <button onclick="alert('Button clicked!')" style="background: white; color: #4CAF50; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Click Me!</button>
</div>
\`\`\``,

    css: `# 🎨 CSS Animation Test

Testing CSS animations in live preview:

\`\`\`css
.test-animation {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  animation: bounce 2s infinite;
  margin: 20px auto;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}
\`\`\`

\`\`\`html
<div class="test-animation"></div>
<p style="text-align: center; color: #666;">Bouncing ball animation</p>
\`\`\``
  };

  return (
    <Card className="w-full max-w-6xl mx-auto bg-slate-800/80 backdrop-blur-sm border-slate-600">
      <CardHeader>
        <CardTitle className="text-white">🧪 Live Preview Testing Suite</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedTest} onValueChange={setSelectedTest} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700">
            <TabsTrigger value="molecular">🧬 Molecular</TabsTrigger>
            <TabsTrigger value="interactive">🎮 Interactive</TabsTrigger>
            <TabsTrigger value="simple">🌟 Simple</TabsTrigger>
            <TabsTrigger value="css">🎨 CSS</TabsTrigger>
          </TabsList>

          {Object.entries(testMessages).map(([key, content]) => (
            <TabsContent key={key} value={key} className="mt-6">
              <div className="bg-slate-900/50 rounded-lg p-6">
                <MessageRenderer content={content} />
              </div>
            </TabsContent>
          ))}
        </Tabs>

        <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
          <h4 className="text-green-300 font-semibold mb-2">✅ Testing Checklist:</h4>
          <ul className="text-green-200 text-sm space-y-1">
            <li>• Code blocks should show syntax highlighting</li>
            <li>• HTML code should render in live preview tabs</li>
            <li>• Interactive elements should be clickable</li>
            <li>• CSS animations should be visible</li>
            <li>• Tabs should switch between code and preview</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
