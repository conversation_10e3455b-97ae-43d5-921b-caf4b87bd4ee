/**
 * Utility functions for processing and enhancing chat messages
 */

export interface MessageEnhancement {
  hasCodeBlocks: boolean;
  hasLivePreview: boolean;
  hasInteractiveDemo: boolean;
  hasDiagrams: boolean;
  hasMath: boolean;
  codeBlocks: CodeBlockInfo[];
  interactiveElements: InteractiveElement[];
}

export interface CodeBlockInfo {
  language: string;
  code: string;
  startIndex: number;
  endIndex: number;
  isLivePreviewCandidate: boolean;
}

export interface InteractiveElement {
  type: 'animation' | 'color_picker' | 'layout' | 'chart';
  parameters: Record<string, any>;
  startIndex: number;
  endIndex: number;
}

/**
 * Analyzes message content to identify enhancement opportunities
 */
export function analyzeMessageContent(content: string): MessageEnhancement {
  const codeBlocks = extractCodeBlocks(content);
  const interactiveElements = extractInteractiveElements(content);
  
  return {
    hasCodeBlocks: codeBlocks.length > 0,
    hasLivePreview: codeBlocks.some(block => isLivePreviewCandidate(block)),
    hasInteractiveDemo: interactiveElements.length > 0,
    hasDiagrams: content.includes('```mermaid') || content.includes('<!-- DIAGRAM -->'),
    hasMath: content.includes('$$') || content.includes('\\(') || content.includes('\\['),
    codeBlocks,
    interactiveElements,
  };
}

/**
 * Extracts code blocks from markdown content
 */
export function extractCodeBlocks(content: string): CodeBlockInfo[] {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const blocks: CodeBlockInfo[] = [];
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const language = match[1] || 'text';
    const code = match[2].trim();
    const startIndex = match.index;
    const endIndex = match.index + match[0].length;

    blocks.push({
      language,
      code,
      startIndex,
      endIndex,
      isLivePreviewCandidate: isLivePreviewCandidate({ language, code } as CodeBlockInfo),
    });
  }

  return blocks;
}

/**
 * Determines if a code block is suitable for live preview
 */
export function isLivePreviewCandidate(block: CodeBlockInfo): boolean {
  const webLanguages = ['html', 'css', 'javascript', 'js'];
  
  if (!webLanguages.includes(block.language.toLowerCase())) {
    return false;
  }

  // Check if the code contains basic web elements
  if (block.language === 'html') {
    return block.code.includes('<') && block.code.includes('>');
  }
  
  if (block.language === 'css') {
    return block.code.includes('{') && block.code.includes('}');
  }
  
  if (['javascript', 'js'].includes(block.language)) {
    // Simple heuristic for executable JS
    return block.code.includes('function') || 
           block.code.includes('=>') || 
           block.code.includes('document') ||
           block.code.includes('console');
  }

  return false;
}

/**
 * Extracts interactive demo markers from content
 */
export function extractInteractiveElements(content: string): InteractiveElement[] {
  const elements: InteractiveElement[] = [];
  
  // CSS Animation demos
  const animationMatch = content.match(/<!-- INTERACTIVE:CSS_ANIMATION -->/);
  if (animationMatch) {
    elements.push({
      type: 'animation',
      parameters: {
        duration: 2,
        delay: 0,
        iterations: 1,
        direction: 'normal',
        timingFunction: 'ease',
      },
      startIndex: animationMatch.index || 0,
      endIndex: (animationMatch.index || 0) + animationMatch[0].length,
    });
  }

  // Color picker demos
  const colorMatch = content.match(/<!-- INTERACTIVE:COLOR_PICKER -->/);
  if (colorMatch) {
    elements.push({
      type: 'color_picker',
      parameters: {
        hue: 200,
        saturation: 70,
        lightness: 50,
        alpha: 1,
      },
      startIndex: colorMatch.index || 0,
      endIndex: (colorMatch.index || 0) + colorMatch[0].length,
    });
  }

  // Layout demos
  const layoutMatch = content.match(/<!-- INTERACTIVE:LAYOUT_DEMO -->/);
  if (layoutMatch) {
    elements.push({
      type: 'layout',
      parameters: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16,
      },
      startIndex: layoutMatch.index || 0,
      endIndex: (layoutMatch.index || 0) + layoutMatch[0].length,
    });
  }

  return elements;
}

/**
 * Formats code for better display
 */
export function formatCode(code: string, language: string): string {
  // Basic code formatting - in a real implementation, you might use prettier or similar
  if (language === 'json') {
    try {
      return JSON.stringify(JSON.parse(code), null, 2);
    } catch {
      return code;
    }
  }

  // Remove excessive whitespace
  return code
    .split('\n')
    .map(line => line.trimEnd())
    .join('\n')
    .trim();
}

/**
 * Generates example code for demonstrations
 */
export function generateExampleCode(type: string): string {
  switch (type) {
    case 'html_basic':
      return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🎨 Beautiful Card Design</h1>
        <p>This is an example of modern web design with glassmorphism effects.</p>
        <button class="button" onclick="changeColor()">Change Color</button>
    </div>
    
    <script>
        function changeColor() {
            const colors = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
            ];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.background = randomColor;
        }
    </script>
</body>
</html>`;

    case 'css_animation':
      return `.animated-box {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}`;

    case 'javascript_interactive':
      return `// Interactive color generator
function generateRandomColor() {
  const hue = Math.floor(Math.random() * 360);
  const saturation = Math.floor(Math.random() * 50) + 50;
  const lightness = Math.floor(Math.random() * 30) + 40;
  
  return \`hsl(\${hue}, \${saturation}%, \${lightness}%)\`;
}

function updateBackground() {
  const color = generateRandomColor();
  document.body.style.backgroundColor = color;
  console.log('New color:', color);
}

// Update color every 3 seconds
setInterval(updateBackground, 3000);`;

    default:
      return '// Example code';
  }
}

/**
 * Detects the programming language from code content
 */
export function detectLanguage(code: string): string {
  // Simple language detection based on common patterns
  if (code.includes('<!DOCTYPE') || code.includes('<html')) return 'html';
  if (code.includes('function') && code.includes('{')) return 'javascript';
  if (code.includes('def ') && code.includes(':')) return 'python';
  if (code.includes('class ') && code.includes('public')) return 'java';
  if (code.includes('#include') || code.includes('int main')) return 'cpp';
  if (code.includes('<?php')) return 'php';
  if (code.includes('SELECT') || code.includes('FROM')) return 'sql';
  if (code.includes('{') && code.includes('}') && code.includes(':')) return 'css';
  
  return 'text';
}
