import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import { CodeBlock } from './CodeBlock';
import { LivePreview } from './LivePreview';
import { InteractiveDemo } from './InteractiveDemo';
import { VisualDiagram } from './VisualDiagram';
import 'katex/dist/katex.min.css';

interface MessageRendererProps {
  content: string;
  isStreaming?: boolean;
}

export const MessageRenderer: React.FC<MessageRendererProps> = ({ 
  content, 
  isStreaming = false 
}) => {
  // Check if content contains special markers for enhanced features
  const hasLivePreview = content.includes('```html') || content.includes('```css') || content.includes('```javascript');
  const hasInteractiveDemo = content.includes('<!-- INTERACTIVE -->');
  const hasDiagram = content.includes('<!-- DIAGRAM -->') || content.includes('```mermaid');

  return (
    <div className="prose prose-invert max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex, rehypeRaw]}
        components={{
          // Enhanced code blocks with syntax highlighting
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';
            const codeContent = String(children).replace(/\n$/, '');

            if (!inline && language) {
              // Check for special code block types
              if (['html', 'css', 'javascript', 'js'].includes(language) && hasLivePreview) {
                return (
                  <LivePreview
                    code={codeContent}
                    language={language}
                    showPreview={true}
                  />
                );
              }

              if (language === 'mermaid') {
                return <VisualDiagram type="mermaid" content={codeContent} />;
              }

              return (
                <CodeBlock
                  code={codeContent}
                  language={language}
                  showLineNumbers={codeContent.split('\n').length > 5}
                />
              );
            }

            // Inline code
            return (
              <code 
                className="bg-slate-800 text-pink-300 px-1.5 py-0.5 rounded text-sm font-mono"
                {...props}
              >
                {children}
              </code>
            );
          },

          // Enhanced headings with better styling
          h1: ({ children }) => (
            <h1 className="text-3xl font-bold text-white mb-6 mt-8 border-b border-slate-600 pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-2xl font-semibold text-white mb-4 mt-6">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-xl font-medium text-white mb-3 mt-4">
              {children}
            </h3>
          ),

          // Enhanced tables
          table: ({ children }) => (
            <div className="overflow-x-auto my-6">
              <table className="min-w-full border border-slate-600 rounded-lg overflow-hidden">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-slate-700">
              {children}
            </thead>
          ),
          th: ({ children }) => (
            <th className="px-4 py-3 text-left text-white font-semibold border-b border-slate-600">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-3 text-slate-200 border-b border-slate-700">
              {children}
            </td>
          ),

          // Enhanced lists
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-2 text-slate-200 my-4">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-2 text-slate-200 my-4">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-slate-200 leading-relaxed">
              {children}
            </li>
          ),

          // Enhanced blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-400 pl-4 py-2 my-4 bg-slate-800/50 rounded-r-lg">
              <div className="text-slate-300 italic">
                {children}
              </div>
            </blockquote>
          ),

          // Enhanced paragraphs
          p: ({ children }) => (
            <p className="text-slate-200 leading-relaxed mb-4">
              {children}
            </p>
          ),

          // Enhanced links
          a: ({ href, children }) => (
            <a 
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 underline transition-colors"
            >
              {children}
            </a>
          ),

          // Enhanced emphasis
          strong: ({ children }) => (
            <strong className="text-white font-semibold">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="text-slate-300 italic">
              {children}
            </em>
          ),

          // Horizontal rules
          hr: () => (
            <hr className="border-slate-600 my-8" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>

      {/* Interactive demos for special content */}
      {hasInteractiveDemo && (
        <InteractiveDemo content={content} />
      )}

      {/* Streaming indicator */}
      {isStreaming && (
        <span className="inline-block w-2 h-4 bg-blue-400 ml-1 animate-pulse" />
      )}
    </div>
  );
};
