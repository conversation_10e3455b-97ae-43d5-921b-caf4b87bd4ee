/**
 * Molecular data library for 3D chemical structure rendering
 */

export interface Atom {
  id: string;
  element: string;
  x: number;
  y: number;
  z: number;
  color: string;
  radius: number;
  electronegativity?: number;
}

export interface Bond {
  id: string;
  atom1: string;
  atom2: string;
  type: 'single' | 'double' | 'triple';
  strength: number;
  length?: number;
}

export interface Molecule {
  id: string;
  name: string;
  formula: string;
  atoms: Atom[];
  bonds: Bond[];
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  molecularWeight?: number;
  boilingPoint?: number;
  meltingPoint?: number;
}

// Element colors based on CPK coloring convention
export const ELEMENT_COLORS = {
  H: '#ffffff',   // Hydrogen - White
  C: '#444444',   // Carbon - Black/Dark Gray
  N: '#3050f8',   // Nitrogen - Blue
  O: '#ff0d0d',   // Oxygen - Red
  F: '#90e050',   // Fluorine - Green
  Cl: '#1ff01f',  // Chlorine - Green
  Br: '#a62929',  // Bromine - Dark Red
  I: '#940094',   // Iodine - Purple
  P: '#ff8000',   // Phosphorus - Orange
  S: '#ffff30',   // Sulfur - Yellow
  Na: '#ab5cf2',  // Sodium - Purple
  Mg: '#8aff00',  // Magnesium - Green
  Al: '#bfa6a6',  // Aluminum - Gray
  Si: '#f0c8a0',  // Silicon - Tan
  K: '#8f40d4',   // Potassium - Purple
  Ca: '#3dff00',  // Calcium - Green
  Fe: '#e06633',  // Iron - Orange
  Cu: '#c88033',  // Copper - Brown
  Zn: '#7d80b0',  // Zinc - Blue-Gray
};

// Van der Waals radii (in picometers, scaled for visualization)
export const ELEMENT_RADII = {
  H: 12,   // Hydrogen
  C: 16,   // Carbon
  N: 15,   // Nitrogen
  O: 14,   // Oxygen
  F: 13,   // Fluorine
  Cl: 18,  // Chlorine
  Br: 20,  // Bromine
  I: 22,   // Iodine
  P: 18,   // Phosphorus
  S: 18,   // Sulfur
  Na: 23,  // Sodium
  Mg: 17,  // Magnesium
  Al: 18,  // Aluminum
  Si: 21,  // Silicon
  K: 28,   // Potassium
  Ca: 20,  // Calcium
  Fe: 17,  // Iron
  Cu: 14,  // Copper
  Zn: 14,  // Zinc
};

// Common molecules library
export const MOLECULES: Record<string, Molecule> = {
  water: {
    id: 'h2o',
    name: 'Water',
    formula: 'H₂O',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 18.015,
    boilingPoint: 100,
    meltingPoint: 0,
    atoms: [
      { id: 'o1', element: 'O', x: 0, y: 0, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O },
      { id: 'h1', element: 'H', x: -25, y: -15, z: 5, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h2', element: 'H', x: 25, y: -15, z: -5, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H }
    ],
    bonds: [
      { id: 'b1', atom1: 'o1', atom2: 'h1', type: 'single', strength: 1, length: 96 },
      { id: 'b2', atom1: 'o1', atom2: 'h2', type: 'single', strength: 1, length: 96 }
    ]
  },

  carbonDioxide: {
    id: 'co2',
    name: 'Carbon Dioxide',
    formula: 'CO₂',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 44.01,
    atoms: [
      { id: 'c1', element: 'C', x: 0, y: 0, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'o1', element: 'O', x: -35, y: 0, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O },
      { id: 'o2', element: 'O', x: 35, y: 0, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O }
    ],
    bonds: [
      { id: 'b1', atom1: 'c1', atom2: 'o1', type: 'double', strength: 2, length: 116 },
      { id: 'b2', atom1: 'c1', atom2: 'o2', type: 'double', strength: 2, length: 116 }
    ]
  },

  methane: {
    id: 'ch4',
    name: 'Methane',
    formula: 'CH₄',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 16.04,
    atoms: [
      { id: 'c1', element: 'C', x: 0, y: 0, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'h1', element: 'H', x: 20, y: 20, z: 20, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h2', element: 'H', x: -20, y: -20, z: 20, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h3', element: 'H', x: -20, y: 20, z: -20, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h4', element: 'H', x: 20, y: -20, z: -20, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H }
    ],
    bonds: [
      { id: 'b1', atom1: 'c1', atom2: 'h1', type: 'single', strength: 1, length: 109 },
      { id: 'b2', atom1: 'c1', atom2: 'h2', type: 'single', strength: 1, length: 109 },
      { id: 'b3', atom1: 'c1', atom2: 'h3', type: 'single', strength: 1, length: 109 },
      { id: 'b4', atom1: 'c1', atom2: 'h4', type: 'single', strength: 1, length: 109 }
    ]
  },

  ammonia: {
    id: 'nh3',
    name: 'Ammonia',
    formula: 'NH₃',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 17.03,
    atoms: [
      { id: 'n1', element: 'N', x: 0, y: 0, z: 0, color: ELEMENT_COLORS.N, radius: ELEMENT_RADII.N },
      { id: 'h1', element: 'H', x: 0, y: -25, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h2', element: 'H', x: -22, y: 12, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h3', element: 'H', x: 22, y: 12, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H }
    ],
    bonds: [
      { id: 'b1', atom1: 'n1', atom2: 'h1', type: 'single', strength: 1, length: 101 },
      { id: 'b2', atom1: 'n1', atom2: 'h2', type: 'single', strength: 1, length: 101 },
      { id: 'b3', atom1: 'n1', atom2: 'h3', type: 'single', strength: 1, length: 101 }
    ]
  },

  carbonicAcid: {
    id: 'h2co3',
    name: 'Carbonic Acid',
    formula: 'H₂CO₃',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 62.03,
    atoms: [
      { id: 'c1', element: 'C', x: 0, y: 0, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'o1', element: 'O', x: -30, y: -20, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O },
      { id: 'o2', element: 'O', x: 30, y: -20, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O },
      { id: 'o3', element: 'O', x: 0, y: 30, z: 0, color: ELEMENT_COLORS.O, radius: ELEMENT_RADII.O },
      { id: 'h1', element: 'H', x: -45, y: -35, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h2', element: 'H', x: 45, y: -35, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H }
    ],
    bonds: [
      { id: 'b1', atom1: 'c1', atom2: 'o1', type: 'single', strength: 1, length: 136 },
      { id: 'b2', atom1: 'c1', atom2: 'o2', type: 'single', strength: 1, length: 136 },
      { id: 'b3', atom1: 'c1', atom2: 'o3', type: 'double', strength: 2, length: 123 },
      { id: 'b4', atom1: 'o1', atom2: 'h1', type: 'single', strength: 1, length: 96 },
      { id: 'b5', atom1: 'o2', atom2: 'h2', type: 'single', strength: 1, length: 96 }
    ]
  },

  benzene: {
    id: 'c6h6',
    name: 'Benzene',
    formula: 'C₆H₆',
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    molecularWeight: 78.11,
    atoms: [
      // Carbon ring
      { id: 'c1', element: 'C', x: 30, y: 0, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'c2', element: 'C', x: 15, y: 26, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'c3', element: 'C', x: -15, y: 26, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'c4', element: 'C', x: -30, y: 0, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'c5', element: 'C', x: -15, y: -26, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      { id: 'c6', element: 'C', x: 15, y: -26, z: 0, color: ELEMENT_COLORS.C, radius: ELEMENT_RADII.C },
      // Hydrogen atoms
      { id: 'h1', element: 'H', x: 45, y: 0, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h2', element: 'H', x: 23, y: 39, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h3', element: 'H', x: -23, y: 39, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h4', element: 'H', x: -45, y: 0, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h5', element: 'H', x: -23, y: -39, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H },
      { id: 'h6', element: 'H', x: 23, y: -39, z: 0, color: ELEMENT_COLORS.H, radius: ELEMENT_RADII.H }
    ],
    bonds: [
      // Carbon ring bonds (alternating single/double)
      { id: 'b1', atom1: 'c1', atom2: 'c2', type: 'single', strength: 1.5, length: 140 },
      { id: 'b2', atom1: 'c2', atom2: 'c3', type: 'double', strength: 1.5, length: 140 },
      { id: 'b3', atom1: 'c3', atom2: 'c4', type: 'single', strength: 1.5, length: 140 },
      { id: 'b4', atom1: 'c4', atom2: 'c5', type: 'double', strength: 1.5, length: 140 },
      { id: 'b5', atom1: 'c5', atom2: 'c6', type: 'single', strength: 1.5, length: 140 },
      { id: 'b6', atom1: 'c6', atom2: 'c1', type: 'double', strength: 1.5, length: 140 },
      // C-H bonds
      { id: 'b7', atom1: 'c1', atom2: 'h1', type: 'single', strength: 1, length: 108 },
      { id: 'b8', atom1: 'c2', atom2: 'h2', type: 'single', strength: 1, length: 108 },
      { id: 'b9', atom1: 'c3', atom2: 'h3', type: 'single', strength: 1, length: 108 },
      { id: 'b10', atom1: 'c4', atom2: 'h4', type: 'single', strength: 1, length: 108 },
      { id: 'b11', atom1: 'c5', atom2: 'h5', type: 'single', strength: 1, length: 108 },
      { id: 'b12', atom1: 'c6', atom2: 'h6', type: 'single', strength: 1, length: 108 }
    ]
  }
};

// Chemical reaction definitions
export interface ChemicalReaction {
  id: string;
  name: string;
  equation: string;
  reactants: string[];
  products: string[];
  type: 'synthesis' | 'decomposition' | 'single_replacement' | 'double_replacement' | 'combustion';
  energyChange: number; // kJ/mol
  description: string;
}

export const REACTIONS: Record<string, ChemicalReaction> = {
  waterFormation: {
    id: 'water_formation',
    name: 'Water Formation',
    equation: '2H₂ + O₂ → 2H₂O',
    reactants: ['h2', 'o2'],
    products: ['water'],
    type: 'synthesis',
    energyChange: -286,
    description: 'Formation of water from hydrogen and oxygen gases'
  },

  carbonicAcidFormation: {
    id: 'carbonic_acid_formation',
    name: 'Carbonic Acid Formation',
    equation: 'H₂O + CO₂ → H₂CO₃',
    reactants: ['water', 'carbonDioxide'],
    products: ['carbonicAcid'],
    type: 'synthesis',
    energyChange: -20,
    description: 'Formation of carbonic acid from water and carbon dioxide'
  },

  methaneCombustion: {
    id: 'methane_combustion',
    name: 'Methane Combustion',
    equation: 'CH₄ + 2O₂ → CO₂ + 2H₂O',
    reactants: ['methane', 'o2'],
    products: ['carbonDioxide', 'water'],
    type: 'combustion',
    energyChange: -890,
    description: 'Complete combustion of methane in oxygen'
  }
};

/**
 * Utility functions for molecular manipulation
 */
export const MolecularUtils = {
  // Clone a molecule with new position
  cloneMolecule: (molecule: Molecule, newPosition: { x: number; y: number; z: number }): Molecule => ({
    ...molecule,
    id: `${molecule.id}_${Date.now()}`,
    position: newPosition,
    atoms: molecule.atoms.map(atom => ({ ...atom, id: `${atom.id}_${Date.now()}` })),
    bonds: molecule.bonds.map(bond => ({ 
      ...bond, 
      id: `${bond.id}_${Date.now()}`,
      atom1: `${bond.atom1}_${Date.now()}`,
      atom2: `${bond.atom2}_${Date.now()}`
    }))
  }),

  // Calculate molecular center of mass
  getCenterOfMass: (molecule: Molecule): { x: number; y: number; z: number } => {
    const totalMass = molecule.atoms.reduce((sum, atom) => sum + (ELEMENT_RADII[atom.element as keyof typeof ELEMENT_RADII] || 1), 0);
    const center = molecule.atoms.reduce(
      (acc, atom) => {
        const mass = ELEMENT_RADII[atom.element as keyof typeof ELEMENT_RADII] || 1;
        return {
          x: acc.x + atom.x * mass,
          y: acc.y + atom.y * mass,
          z: acc.z + atom.z * mass
        };
      },
      { x: 0, y: 0, z: 0 }
    );
    
    return {
      x: center.x / totalMass,
      y: center.y / totalMass,
      z: center.z / totalMass
    };
  },

  // Get molecule by ID
  getMolecule: (id: string): Molecule | undefined => MOLECULES[id],

  // Get reaction by ID
  getReaction: (id: string): ChemicalReaction | undefined => REACTIONS[id]
};
