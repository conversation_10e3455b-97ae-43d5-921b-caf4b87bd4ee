import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Download, 
  Copy, 
  Maximize2, 
  Code, 
  Eye,
  Settings,
  Zap
} from 'lucide-react';

interface CodeEditorProps {
  initialCode?: {
    html?: string;
    css?: string;
    javascript?: string;
  };
  onCodeChange?: (language: string, code: string) => void;
  showPreview?: boolean;
  autoRun?: boolean;
  theme?: 'dark' | 'light';
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  initialCode = {},
  onCodeChange,
  showPreview = true,
  autoRun = true,
  theme = 'dark'
}) => {
  const [activeTab, setActiveTab] = useState('html');
  const [isRunning, setIsRunning] = useState(autoRun);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [code, setCode] = useState({
    html: initialCode.html || '<div class="demo-container">\n  <h1>Hello World!</h1>\n  <p>Edit the code to see changes in real-time.</p>\n</div>',
    css: initialCode.css || '.demo-container {\n  padding: 20px;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  color: white;\n  border-radius: 10px;\n  text-align: center;\n  font-family: Arial, sans-serif;\n}\n\nh1 {\n  margin: 0 0 10px 0;\n  font-size: 2em;\n}\n\np {\n  margin: 0;\n  opacity: 0.9;\n}',
    javascript: initialCode.javascript || '// Interactive JavaScript\ndocument.addEventListener("DOMContentLoaded", function() {\n  const container = document.querySelector(".demo-container");\n  \n  if (container) {\n    container.addEventListener("click", function() {\n      this.style.transform = this.style.transform === "scale(1.1)" ? "scale(1)" : "scale(1.1)";\n      this.style.transition = "transform 0.3s ease";\n    });\n  }\n});'
  });
  
  const previewRef = useRef<HTMLIFrameElement>(null);
  const [lineNumbers, setLineNumbers] = useState<{ [key: string]: number }>({});

  // Update preview when code changes
  useEffect(() => {
    if (isRunning && showPreview) {
      updatePreview();
    }
  }, [code, isRunning]);

  // Calculate line numbers
  useEffect(() => {
    const newLineNumbers: { [key: string]: number } = {};
    Object.entries(code).forEach(([lang, codeContent]) => {
      newLineNumbers[lang] = codeContent.split('\n').length;
    });
    setLineNumbers(newLineNumbers);
  }, [code]);

  const updatePreview = () => {
    if (!previewRef.current) return;

    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        ${code.css}
    </style>
</head>
<body>
    ${code.html}
    <script>
        try {
            ${code.javascript}
        } catch (error) {
            console.error('JavaScript Error:', error);
        }
    </script>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    previewRef.current.src = url;
  };

  const handleCodeChange = (language: string, newCode: string) => {
    setCode(prev => ({ ...prev, [language]: newCode }));
    onCodeChange?.(language, newCode);
  };

  const copyCode = () => {
    const allCode = `<!-- HTML -->\n${code.html}\n\n/* CSS */\n${code.css}\n\n// JavaScript\n${code.javascript}`;
    navigator.clipboard.writeText(allCode);
  };

  const downloadCode = () => {
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Code</title>
    <style>
        ${code.css}
    </style>
</head>
<body>
    ${code.html}
    <script>
        ${code.javascript}
    </script>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'code-preview.html';
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetCode = () => {
    setCode({
      html: '<div class="demo-container">\n  <h1>Hello World!</h1>\n  <p>Edit the code to see changes in real-time.</p>\n</div>',
      css: '.demo-container {\n  padding: 20px;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  color: white;\n  border-radius: 10px;\n  text-align: center;\n  font-family: Arial, sans-serif;\n}\n\nh1 {\n  margin: 0 0 10px 0;\n  font-size: 2em;\n}\n\np {\n  margin: 0;\n  opacity: 0.9;\n}',
      javascript: '// Interactive JavaScript\ndocument.addEventListener("DOMContentLoaded", function() {\n  const container = document.querySelector(".demo-container");\n  \n  if (container) {\n    container.addEventListener("click", function() {\n      this.style.transform = this.style.transform === "scale(1.1)" ? "scale(1)" : "scale(1.1)";\n      this.style.transition = "transform 0.3s ease";\n    });\n  }\n});'
    });
  };

  const getLanguageIcon = (lang: string) => {
    switch (lang) {
      case 'html': return '🌐';
      case 'css': return '🎨';
      case 'javascript': return '⚡';
      default: return '📄';
    }
  };

  const getLanguageColor = (lang: string) => {
    switch (lang) {
      case 'html': return 'bg-orange-500';
      case 'css': return 'bg-blue-500';
      case 'javascript': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className={`w-full ${isFullscreen ? 'fixed inset-0 z-50' : ''} bg-slate-800/80 backdrop-blur-sm border-slate-600`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Real-time Code Editor
            <Badge variant="secondary" className="ml-2">Live</Badge>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsRunning(!isRunning)}
              className={isRunning ? 'text-green-400' : 'text-slate-400'}
            >
              {isRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={copyCode}>
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={downloadCode}>
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={resetCode}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsFullscreen(!isFullscreen)}>
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className={`grid ${showPreview ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'} gap-6 ${isFullscreen ? 'h-[calc(100vh-120px)]' : 'h-96'}`}>
          {/* Code Editor Section */}
          <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-slate-700">
                {Object.entries(code).map(([lang, codeContent]) => (
                  <TabsTrigger key={lang} value={lang} className="flex items-center gap-2">
                    <span>{getLanguageIcon(lang)}</span>
                    {lang.toUpperCase()}
                    <Badge variant="outline" className="text-xs">
                      {lineNumbers[lang] || 0}
                    </Badge>
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.entries(code).map(([lang, codeContent]) => (
                <TabsContent key={lang} value={lang} className="mt-4">
                  <div className="relative">
                    <div className={`absolute top-2 left-2 w-3 h-3 rounded-full ${getLanguageColor(lang)} opacity-60`}></div>
                    <textarea
                      value={codeContent}
                      onChange={(e) => handleCodeChange(lang, e.target.value)}
                      className={`w-full h-80 p-4 pl-8 bg-slate-900 text-white font-mono text-sm border border-slate-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 ${theme === 'dark' ? 'bg-slate-900' : 'bg-white text-black'}`}
                      placeholder={`Enter ${lang.toUpperCase()} code here...`}
                      spellCheck={false}
                      style={{
                        lineHeight: '1.5',
                        tabSize: 2
                      }}
                    />
                    <div className="absolute bottom-2 right-2 text-xs text-slate-400">
                      Lines: {lineNumbers[lang] || 0} | Characters: {codeContent.length}
                    </div>
                  </div>
                </TabsContent>
              ))}
            </Tabs>

            {/* Code Stats */}
            <div className="bg-slate-700/50 rounded-lg p-3">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-orange-400 font-semibold">{code.html.split('\n').length}</div>
                  <div className="text-slate-400">HTML Lines</div>
                </div>
                <div className="text-center">
                  <div className="text-blue-400 font-semibold">{code.css.split('\n').length}</div>
                  <div className="text-slate-400">CSS Lines</div>
                </div>
                <div className="text-center">
                  <div className="text-yellow-400 font-semibold">{code.javascript.split('\n').length}</div>
                  <div className="text-slate-400">JS Lines</div>
                </div>
              </div>
            </div>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-white font-semibold flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Live Preview
                </h3>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isRunning ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                  <span className="text-xs text-slate-400">
                    {isRunning ? 'Live' : 'Paused'}
                  </span>
                </div>
              </div>
              
              <div className="bg-white rounded-lg border border-slate-600 overflow-hidden">
                <iframe
                  ref={previewRef}
                  className="w-full h-80 border-none"
                  title="Live Preview"
                  sandbox="allow-scripts allow-same-origin"
                />
              </div>

              {/* Preview Controls */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={updatePreview} className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Refresh
                </Button>
                <Button variant="outline" size="sm" onClick={() => setIsRunning(!isRunning)} className="flex items-center gap-2">
                  {isRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  {isRunning ? 'Pause' : 'Run'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
