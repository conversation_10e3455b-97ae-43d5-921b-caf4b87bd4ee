
import { useState, useCallback } from 'react';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
}

interface FunctionDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: object;
    strict?: boolean;
  };
}

interface UsePuterAIOptions {
  model?: string;
  stream?: boolean;
  tools?: FunctionDefinition[];
}

export const usePuterAI = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {},
    imageURL?: string | string[]
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const { model = 'gpt-4o-mini', stream = false, tools, testMode = false } = options;

      // Check if Puter AI is available
      if (!(window as any).puter?.ai?.chat) {
        // Fallback to demo mode with enhanced response examples
        return generateDemoResponse(promptOrMessages, options);
      }

      // Handle different parameter combinations based on Puter.js documentation
      let response;

      if (imageURL) {
        // Image + prompt combination: puter.ai.chat(prompt, imageURL, testMode, options)
        response = await (window as any).puter.ai.chat(
          promptOrMessages as string,
          imageURL,
          testMode,
          { model, stream, tools }
        );
      } else if (Array.isArray(promptOrMessages)) {
        // Messages array: puter.ai.chat([messages], testMode, options)
        response = await (window as any).puter.ai.chat(
          promptOrMessages,
          testMode,
          { model, stream, tools }
        );
      } else {
        // Simple prompt: puter.ai.chat(prompt, options) or puter.ai.chat(prompt, testMode, options)
        if (testMode) {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            testMode,
            { model, stream, tools }
          );
        } else {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            { model, stream, tools }
          );
        }
      }

      return response;
    } catch (err) {
      console.warn('Puter AI API error, falling back to demo mode:', err);
      // Fallback to demo mode if API fails
      return generateDemoResponse(promptOrMessages, options);
    } finally {
      if (!options.stream) {
        setIsLoading(false);
      }
    }
  }, []);

  // Demo response generator for testing enhanced features
  const generateDemoResponse = async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {}
  ) => {
    // Extract the user's message
    let userMessage = '';
    if (Array.isArray(promptOrMessages)) {
      const lastMessage = promptOrMessages[promptOrMessages.length - 1];
      userMessage = lastMessage?.content || '';
    } else {
      userMessage = promptOrMessages;
    }

    // Generate enhanced demo responses based on keywords
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('3d') && (lowerMessage.includes('molecular') || lowerMessage.includes('chemical'))) {
      return generateMolecularDemo();
    } else if (lowerMessage.includes('html') || lowerMessage.includes('web') || lowerMessage.includes('website')) {
      return generateHTMLDemo();
    } else if (lowerMessage.includes('css') || lowerMessage.includes('animation')) {
      return generateCSSDemo();
    } else if (lowerMessage.includes('javascript') || lowerMessage.includes('interactive')) {
      return generateJavaScriptDemo();
    } else if (lowerMessage.includes('chart') || lowerMessage.includes('graph') || lowerMessage.includes('diagram')) {
      return generateDiagramDemo();
    } else {
      return generateBasicDemo(userMessage);
    }
  };

  const generateImage = useCallback(async (prompt: string, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const image = await (window as any).puter.ai.txt2img(prompt, testMode);
      return image;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const transcribeImage = useCallback(async (image: string | File | Blob, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const text = await (window as any).puter.ai.img2txt(image, testMode);
      return text;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to transcribe image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const textToSpeech = useCallback(async (text: string, language = 'en-US', testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const audio = await (window as any).puter.ai.txt2speech(text, language, testMode);
      return audio;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate speech';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    sendMessage,
    generateImage,
    transcribeImage,
    textToSpeech,
    isLoading,
    error,
    setIsLoading,
  };
};
