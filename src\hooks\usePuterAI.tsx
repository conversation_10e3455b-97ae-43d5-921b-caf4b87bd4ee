
import { useState, useCallback } from 'react';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
}

interface FunctionDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: object;
    strict?: boolean;
  };
}

interface UsePuterAIOptions {
  model?: string;
  stream?: boolean;
  tools?: FunctionDefinition[];
}

export const usePuterAI = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {},
    imageURL?: string | string[]
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const { model = 'gpt-4o-mini', stream = false, tools, testMode = false } = options;

      // Check if Puter AI is available
      if (!(window as any).puter?.ai?.chat) {
        // Fallback to demo mode with enhanced response examples
        return generateDemoResponse(promptOrMessages, options);
      }

      // Handle different parameter combinations based on Puter.js documentation
      let response;

      if (imageURL) {
        // Image + prompt combination: puter.ai.chat(prompt, imageURL, testMode, options)
        response = await (window as any).puter.ai.chat(
          promptOrMessages as string,
          imageURL,
          testMode,
          { model, stream, tools }
        );
      } else if (Array.isArray(promptOrMessages)) {
        // Messages array: puter.ai.chat([messages], testMode, options)
        response = await (window as any).puter.ai.chat(
          promptOrMessages,
          testMode,
          { model, stream, tools }
        );
      } else {
        // Simple prompt: puter.ai.chat(prompt, options) or puter.ai.chat(prompt, testMode, options)
        if (testMode) {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            testMode,
            { model, stream, tools }
          );
        } else {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            { model, stream, tools }
          );
        }
      }

      return response;
    } catch (err) {
      console.warn('Puter AI API error, falling back to demo mode:', err);
      // Fallback to demo mode if API fails
      return generateDemoResponse(promptOrMessages, options);
    } finally {
      if (!options.stream) {
        setIsLoading(false);
      }
    }
  }, []);

  // Demo response generator for testing enhanced features
  const generateDemoResponse = async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {}
  ) => {
    // Extract the user's message
    let userMessage = '';
    if (Array.isArray(promptOrMessages)) {
      const lastMessage = promptOrMessages[promptOrMessages.length - 1];
      userMessage = lastMessage?.content || '';
    } else {
      userMessage = promptOrMessages;
    }

    // Generate enhanced demo responses based on keywords
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('3d') && (lowerMessage.includes('molecular') || lowerMessage.includes('chemical'))) {
      return generateMolecularDemo();
    } else if (lowerMessage.includes('html') || lowerMessage.includes('web') || lowerMessage.includes('website')) {
      return generateHTMLDemo();
    } else if (lowerMessage.includes('css') || lowerMessage.includes('animation')) {
      return generateCSSDemo();
    } else if (lowerMessage.includes('javascript') || lowerMessage.includes('interactive')) {
      return generateJavaScriptDemo();
    } else if (lowerMessage.includes('chart') || lowerMessage.includes('graph') || lowerMessage.includes('diagram')) {
      return generateDiagramDemo();
    } else {
      return generateBasicDemo(userMessage);
    }
  };

  // Demo response generators
  const generateMolecularDemo = () => {
    return {
      message: {
        content: `# 🧬 3D Molecular Animation Demo

I'll create a 3D SVG animation showing a chemical reaction between water molecules (H₂O) and carbon dioxide (CO₂) forming carbonic acid (H₂CO₃).

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Molecular Reaction Animation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: Arial, sans-serif;
            color: white;
            overflow-x: auto;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .reaction-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .molecule-stage {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }

        .molecule {
            perspective: 1000px;
            margin: 20px;
        }

        .molecule-3d {
            transform-style: preserve-3d;
            animation: rotate3d 4s infinite linear;
        }

        .atom {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .hydrogen {
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #ffffff, #cccccc);
            color: #333;
        }

        .oxygen {
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #ff4444, #cc0000);
            color: white;
        }

        .carbon {
            width: 35px;
            height: 35px;
            background: radial-gradient(circle, #444444, #000000);
            color: white;
        }

        .bond {
            position: absolute;
            background: #ffffff;
            transform-origin: left center;
        }

        .single-bond {
            height: 2px;
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }

        .double-bond {
            height: 4px;
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
        }

        /* Water molecule (H₂O) */
        .water .h1 { transform: translate3d(-20px, -15px, 0px); }
        .water .o1 { transform: translate3d(0px, 0px, 0px); }
        .water .h2 { transform: translate3d(20px, -15px, 0px); }
        .water .bond1 {
            width: 25px;
            transform: translate3d(-15px, -7px, 0px) rotate(-30deg);
        }
        .water .bond2 {
            width: 25px;
            transform: translate3d(5px, -7px, 0px) rotate(30deg);
        }

        /* CO₂ molecule */
        .co2 .c1 { transform: translate3d(0px, 0px, 0px); }
        .co2 .o1 { transform: translate3d(-35px, 0px, 0px); }
        .co2 .o2 { transform: translate3d(35px, 0px, 0px); }
        .co2 .bond1 {
            width: 30px;
            transform: translate3d(-30px, -2px, 0px);
        }
        .co2 .bond2 {
            width: 30px;
            transform: translate3d(5px, -2px, 0px);
        }

        /* Carbonic acid (H₂CO₃) */
        .carbonic .c1 { transform: translate3d(0px, 0px, 0px); }
        .carbonic .o1 { transform: translate3d(-30px, -20px, 0px); }
        .carbonic .o2 { transform: translate3d(30px, -20px, 0px); }
        .carbonic .o3 { transform: translate3d(0px, 30px, 0px); }
        .carbonic .h1 { transform: translate3d(-45px, -35px, 0px); }
        .carbonic .h2 { transform: translate3d(45px, -35px, 0px); }

        .arrow {
            font-size: 30px;
            color: #ffff00;
            animation: pulse 2s infinite;
        }

        .reaction-equation {
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        .controls {
            margin: 20px 0;
        }

        .control-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .control-btn:hover {
            background: #45a049;
        }

        @keyframes rotate3d {
            0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
            33% { transform: rotateX(120deg) rotateY(120deg) rotateZ(0deg); }
            66% { transform: rotateX(240deg) rotateY(240deg) rotateZ(120deg); }
            100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .phase-1 { animation-delay: 0s; }
        .phase-2 { animation-delay: 2s; }
        .phase-3 { animation-delay: 4s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧬 3D Molecular Reaction Animation</h1>
        <p>Watch as water (H₂O) and carbon dioxide (CO₂) react to form carbonic acid (H₂CO₃)</p>

        <div class="reaction-equation">
            H₂O + CO₂ → H₂CO₃
        </div>

        <div class="reaction-container">
            <div class="molecule-stage">
                <!-- Water molecule -->
                <div class="molecule phase-1">
                    <div class="molecule-3d water">
                        <div class="atom hydrogen h1">H</div>
                        <div class="atom oxygen o1">O</div>
                        <div class="atom hydrogen h2">H</div>
                        <div class="bond single-bond bond1"></div>
                        <div class="bond single-bond bond2"></div>
                    </div>
                    <div>H₂O</div>
                </div>

                <div class="arrow">+</div>

                <!-- CO₂ molecule -->
                <div class="molecule phase-1">
                    <div class="molecule-3d co2">
                        <div class="atom carbon c1">C</div>
                        <div class="atom oxygen o1">O</div>
                        <div class="atom oxygen o2">O</div>
                        <div class="bond double-bond bond1"></div>
                        <div class="bond double-bond bond2"></div>
                    </div>
                    <div>CO₂</div>
                </div>

                <div class="arrow">→</div>

                <!-- Carbonic acid molecule -->
                <div class="molecule phase-2">
                    <div class="molecule-3d carbonic">
                        <div class="atom carbon c1">C</div>
                        <div class="atom oxygen o1">O</div>
                        <div class="atom oxygen o2">O</div>
                        <div class="atom oxygen o3">O</div>
                        <div class="atom hydrogen h1">H</div>
                        <div class="atom hydrogen h2">H</div>
                    </div>
                    <div>H₂CO₃</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="restartAnimation()">🔄 Restart Animation</button>
            <button class="control-btn" onclick="toggleRotation()">⏸️ Pause/Play</button>
            <button class="control-btn" onclick="changeSpeed()">⚡ Change Speed</button>
        </div>
    </div>

    <script>
        let isPaused = false;
        let currentSpeed = 1;

        function restartAnimation() {
            const molecules = document.querySelectorAll('.molecule-3d');
            molecules.forEach(mol => {
                mol.style.animation = 'none';
                mol.offsetHeight; // Trigger reflow
                mol.style.animation = null;
            });
        }

        function toggleRotation() {
            const molecules = document.querySelectorAll('.molecule-3d');
            isPaused = !isPaused;

            molecules.forEach(mol => {
                if (isPaused) {
                    mol.style.animationPlayState = 'paused';
                } else {
                    mol.style.animationPlayState = 'running';
                }
            });
        }

        function changeSpeed() {
            currentSpeed = currentSpeed === 1 ? 0.5 : currentSpeed === 0.5 ? 2 : 1;
            const molecules = document.querySelectorAll('.molecule-3d');

            molecules.forEach(mol => {
                mol.style.animationDuration = \`\${4 / currentSpeed}s\`;
            });
        }

        // Add click interactions
        document.querySelectorAll('.molecule').forEach(mol => {
            mol.addEventListener('click', function() {
                this.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 300);
            });
        });
    </script>
</body>
</html>
\`\`\`

## 🔬 **Features of this 3D Molecular Animation:**

1. **3D Rotation**: Molecules rotate in 3D space using CSS transforms
2. **Realistic Atoms**: Different colored spheres represent different elements
3. **Chemical Bonds**: Visual bonds between atoms with appropriate styling
4. **Interactive Controls**: Pause, restart, and speed controls
5. **Responsive Design**: Works on different screen sizes
6. **Educational Value**: Shows actual molecular structures and reactions

## 🎮 **Interactive Elements:**

- **Click molecules** to see them scale up
- **Control buttons** to manipulate the animation
- **3D perspective** that rotates continuously
- **Realistic molecular geometry** based on actual chemical structures

This demonstrates how 3D SVG and CSS can create engaging scientific visualizations!`
      }
    };
  };

  const generateImage = useCallback(async (prompt: string, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const image = await (window as any).puter.ai.txt2img(prompt, testMode);
      return image;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const transcribeImage = useCallback(async (image: string | File | Blob, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const text = await (window as any).puter.ai.img2txt(image, testMode);
      return text;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to transcribe image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateHTMLDemo = () => {
    return {
      message: {
        content: `# 🌐 Interactive HTML Demo

I'll create a modern, responsive web component with interactive features:

\`\`\`html
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; color: white; text-align: center; max-width: 500px; margin: 20px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
    <h2 style="margin: 0 0 15px 0; font-size: 24px;">🎨 Interactive Card</h2>
    <p style="margin: 0 0 20px 0; opacity: 0.9;">Click the button to see magic happen!</p>
    <button onclick="changeColors()" style="background: #4CAF50; color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">✨ Change Colors</button>
    <div id="colorDisplay" style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px; font-family: monospace;">
        Current: #667eea → #764ba2
    </div>
</div>

<script>
function changeColors() {
    const colors = [
        ['#667eea', '#764ba2'],
        ['#f093fb', '#f5576c'],
        ['#4facfe', '#00f2fe'],
        ['#43e97b', '#38f9d7'],
        ['#fa709a', '#fee140']
    ];

    const randomPair = colors[Math.floor(Math.random() * colors.length)];
    const card = document.querySelector('div[style*="background: linear-gradient"]');
    const display = document.getElementById('colorDisplay');

    card.style.background = \`linear-gradient(135deg, \${randomPair[0]} 0%, \${randomPair[1]} 100%)\`;
    display.textContent = \`Current: \${randomPair[0]} → \${randomPair[1]}\`;

    // Add animation effect
    card.style.transform = 'scale(1.05)';
    setTimeout(() => {
        card.style.transform = 'scale(1)';
    }, 200);
}
</script>
\`\`\`

This creates a beautiful, interactive card with gradient backgrounds that change on click!`
      }
    };
  };

  const generateCSSDemo = () => {
    return {
      message: {
        content: `# 🎨 CSS Animation Demo

Here's an interactive CSS animation with controls:

<!-- INTERACTIVE:CSS_ANIMATION -->

\`\`\`css
.bouncing-ball {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  animation: bounce 2s infinite ease-in-out;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-50px) scale(1.1);
  }
}

.pulse-ring {
  width: 100px;
  height: 100px;
  border: 3px solid #4ecdc4;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
\`\`\`

Use the interactive controls above to adjust the animation properties and see how they affect the movement!`
      }
    };
  };

  const generateJavaScriptDemo = () => {
    return {
      message: {
        content: `# ⚡ Interactive JavaScript Demo

I'll create a dynamic color generator with interactive elements:

\`\`\`javascript
// Interactive Color Generator
function createColorfulDiv() {
  const div = document.createElement('div');
  const hue = Math.random() * 360;
  const saturation = Math.random() * 50 + 50;
  const lightness = Math.random() * 30 + 40;

  div.style.cssText = \`
    width: 80px;
    height: 80px;
    background: hsl(\${hue}, \${saturation}%, \${lightness}%);
    margin: 5px;
    border-radius: 10px;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  \`;

  div.addEventListener('click', () => {
    const newHue = Math.random() * 360;
    div.style.background = \`hsl(\${newHue}, \${saturation}%, \${lightness}%)\`;
    div.style.transform = 'scale(1.2)';
    setTimeout(() => {
      div.style.transform = 'scale(1)';
    }, 200);
  });

  div.addEventListener('mouseenter', () => {
    div.style.transform = 'scale(1.1) rotate(5deg)';
  });

  div.addEventListener('mouseleave', () => {
    div.style.transform = 'scale(1) rotate(0deg)';
  });

  return div;
}

// Create initial set of colorful divs
const container = document.createElement('div');
container.style.cssText = 'text-align: center; padding: 20px;';

for (let i = 0; i < 8; i++) {
  container.appendChild(createColorfulDiv());
}

// Add control button
const addButton = document.createElement('button');
addButton.textContent = '+ Add More Colors';
addButton.style.cssText = \`
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin: 10px;
  font-size: 16px;
\`;

addButton.addEventListener('click', () => {
  container.insertBefore(createColorfulDiv(), addButton);
});

container.appendChild(addButton);
document.body.appendChild(container);
\`\`\`

**Try it out!** Click on any colored square to change its color, or hover to see animations!`
      }
    };
  };

  const generateDiagramDemo = () => {
    return {
      message: {
        content: `# 📊 Visual Diagram Demo

Here's an interactive flowchart showing a web development process:

<!-- DIAGRAM -->

\`\`\`mermaid
graph TD
    A[Start Project] --> B{Choose Framework}
    B -->|React| C[Setup React App]
    B -->|Vue| D[Setup Vue App]
    B -->|Angular| E[Setup Angular App]
    C --> F[Install Dependencies]
    D --> F
    E --> F
    F --> G[Design Components]
    G --> H[Implement Features]
    H --> I{Testing}
    I -->|Pass| J[Deploy]
    I -->|Fail| K[Fix Bugs]
    K --> H
    J --> L[Monitor & Maintain]
\`\`\`

This diagram shows the typical workflow for web development projects with decision points and feedback loops.`
      }
    };
  };

  const generateBasicDemo = (userMessage: string) => {
    return {
      message: {
        content: `# 💬 Enhanced Response Demo

Thank you for your message: "${userMessage}"

I'm currently running in **demo mode** to showcase the enhanced response features. Here are some examples you can try:

## 🎯 **Try These Commands:**

### For 3D Molecular Animations:
- "Create a 3D animation for chemical molecular reactions"
- "Show me 3D SVG code for molecular structures"

### For Interactive HTML:
- "Create an interactive HTML example"
- "Build a responsive web component"

### For CSS Animations:
- "Show me CSS animation examples"
- "Create animated elements with CSS"

### For JavaScript Demos:
- "Build an interactive JavaScript demo"
- "Create a dynamic color generator"

### For Visual Diagrams:
- "Create a flowchart diagram"
- "Show me a process visualization"

## ✨ **Enhanced Features Available:**

1. **Live Code Previews** - See HTML, CSS, and JavaScript in action
2. **Interactive Demonstrations** - Adjust parameters in real-time
3. **3D Molecular Animations** - Scientific visualizations
4. **Visual Diagrams** - Flowcharts and process maps
5. **Rich Formatting** - Beautiful typography and layouts

Try any of the suggested commands above to see these features in action!`
      }
    };
  };

  const textToSpeech = useCallback(async (text: string, language = 'en-US', testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const audio = await (window as any).puter.ai.txt2speech(text, language, testMode);
      return audio;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate speech';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    sendMessage,
    generateImage,
    transcribeImage,
    textToSpeech,
    isLoading,
    error,
    setIsLoading,
  };
};
